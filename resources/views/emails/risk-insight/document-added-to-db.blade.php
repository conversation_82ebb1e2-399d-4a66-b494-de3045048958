@extends('emails.layout')

@section('alert')
    alert-success
@stop

@section('title')
    Risk Insight Document Added to Database
@stop

@section('content')
    <tr style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
        <td class="content-wrap" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; padding: 20px 0; box-sizing: border-box; font-size: 16px; vertical-align: top; margin: 0;" valign="top">
            <table width="100%" cellpadding="0" cellspacing="0" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
                <tr style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
                    <td class="content-block" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                        Dear {{ $fullName }},
                    </td>
                </tr>
                <tr style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
                    <td class="content-block" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                        The document you submitted has been processed by our system. It has now been added to our database and marked as complete. You can use the link below to view it.
                    </td>
                </tr>
                <tr style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
                    <td class="content-block" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                        <a href="{{ $documentLink }}">{{ $documentLink }}</a>
                    </td>
                </tr>
                <tr style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
                    <td class="content-block" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                        This link will expire in 2 days.
                    </td>
                </tr>
                <tr style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; margin: 0; padding: 0;">
                    <td class="content-block" style="font-family: Arial, Helvetica, sans-serif; line-height: 25px; color: #595E62; box-sizing: border-box; font-size: 16px; vertical-align: top; margin: 0; padding: 0 0 20px;" valign="top">
                        Thank you,<br />
                        Liberty Mutual Insurance
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    @if(isset($unsubscribe_token))
        <tr>
            <td style="text-align:center;font-size:10px;padding:0 20px" align="center">
                <p style="text-align:center;font-size:10px;color:#5c6064;line-height:14px;font-family:Arial,Helvetica,sans-serif" align="center"> 
                    <a style="text-decoration:underline" href="<?php echo Config::get('app.client_frontend') ?>/unsubscribe-email/{{$unsubscribe_token}}" target="_blank"> Unsubscribe </a>
                </p>
            </td>
        </tr>
    @endif
@stop
