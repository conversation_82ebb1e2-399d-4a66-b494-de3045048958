<?php

use App\Http\Controllers\Api\V1\CyberVR\CyberGuestController;
use App\Http\Controllers\Api\V1\AccidentReportingController;
use App\Http\Controllers\Api\V1\AccountDocumentController;
use App\Http\Controllers\Api\V1\BranchController;
use App\Http\Controllers\Api\V1\BrokerController;
use App\Http\Controllers\Api\V1\BrokerUserController;
use App\Http\Controllers\Api\V1\CMS\CmsController;
use App\Http\Controllers\Api\V1\CMS\ProductController as CMSProductController;
use App\Http\Controllers\Api\V1\CMS\SectorSubsectorController;
use App\Http\Controllers\Api\V1\CMS\VirtualRoomsOptionsController;
use App\Http\Controllers\Api\V1\Community\FileController;
use App\Http\Controllers\Api\V1\Community\MessageController;
use App\Http\Controllers\Api\V1\Community\ReplyController;
use App\Http\Controllers\Api\V1\Community\SectorController;
use App\Http\Controllers\Api\V1\Community\TagController;
use App\Http\Controllers\Api\V1\DocumentManagementController;
use App\Http\Controllers\Api\V1\ExternalSurveyCompanyController;
use App\Http\Controllers\Api\V1\ExternalSurveyorController;
use App\Http\Controllers\Api\V1\LetsTalk\VideoCallController;
use App\Http\Controllers\Api\V1\LibertyUserController;
use App\Http\Controllers\Api\V1\LogController;
use App\Http\Controllers\Api\V1\MgaSchemeController;
use App\Http\Controllers\Api\V1\OrganisationClaimController;
use App\Http\Controllers\Api\V1\OrganisationContactController;
use App\Http\Controllers\Api\V1\OrganisationController;
use App\Http\Controllers\Api\V1\OrganisationNoteController;
use App\Http\Controllers\Api\V1\OrganisationOverviewController;
use App\Http\Controllers\Api\V1\OrganisationReportController;
use App\Http\Controllers\Api\V1\RHS\CustomerController;
use App\Http\Controllers\Api\V1\RHS\OrderController;
use App\Http\Controllers\Api\V1\RHS\OrganisationController as RHSOrganisationController;
use App\Http\Controllers\Api\V1\RHS\ProductController;
use App\Http\Controllers\Api\V1\RHS\TestController;
use App\Http\Controllers\Api\V1\RiskInsights\BenchmarkController as RiskInsightsBenchmarkController;
use App\Http\Controllers\Api\V1\RiskInsights\DashboardController as RiskInsightsDashboardController;
use App\Http\Controllers\Api\V1\RiskInsights\PortfolioController as RiskInsightsPortfolioController;
use App\Http\Controllers\Api\V1\RiskInsights\PollController as RiskInsightsPollController;
use App\Http\Controllers\Api\V1\RiskInsights\RiskInsightController;
use App\Http\Controllers\Api\V1\RiskRecommendationCardsController;
use App\Http\Controllers\Api\V1\RRAppetite\AuthController;
use App\Http\Controllers\Api\V1\RRAppetite\FormController;
use App\Http\Controllers\Api\V1\RRAppetite\IndexController;
use App\Http\Controllers\Api\V1\RRAppetite\NewFormController;
use App\Http\Controllers\Api\V1\RRAppetite\ProductController as RRAppetiteProductController;
use App\Http\Controllers\Api\V1\RRAppetite\RegionController;
use App\Http\Controllers\Api\V1\RRAppetite\SectorController as RRAppetiteSectorController;
use App\Http\Controllers\Api\V1\RRAppetite\SicCodeController;
use App\Http\Controllers\Api\V1\RRAppetite\SubsectorController;
use App\Http\Controllers\Api\V1\SearchController;
use App\Http\Controllers\Api\V1\SocialLoginController;
use App\Http\Controllers\Api\V1\TradeGroupsController;
use App\Http\Controllers\Api\V1\TradesController;
use App\Http\Controllers\Api\V1\UserController;
use App\Http\Controllers\Api\V1\OrganisationSectionSettingController;
use App\Http\Controllers\Api\V1\MicrositeController;
use App\Http\Controllers\OrganisationKeyAccountController;
use App\Http\Controllers\Api\V1\APIControllerForCommand;
use App\Http\Controllers\Api\V1\OrganisationPolicyLogController;
use App\Http\Controllers\Api\V1\PauseUserNotificationController;
use App\Http\Controllers\Api\V1\RiskInsights\RiskInsightBoardController;
use App\Models\RiskRecommendationCards;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

//$user = App\Models\User::find(7);
//\Illuminate\Support\Facades\Auth::login($user);

//healthcheck
Route::get('/healthcheck', function () {
    return response()->json([
        'status' => 200,
        'message' => 'OK'
    ]);
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group(['prefix' => 'v1'], function () {
    // reset liberty user/external surveyor 2fa
    Route::post('reset-lu-tfa', [LibertyUserController::class, 'resetTfa']);
    Route::post('reset-es-tfa', [ExternalSurveyorController::class, 'resetTfa']);
    Route::post('reset-bu-tfa', [BrokerUserController::class, 'resetTfa']); /* completed */
    Route::post('reset-cl-tfa', [UserController::class, 'resetTfa']);

    Route::post('validated/activate', [LibertyUserController::class, 'validated']);
    Route::post('established/activate', [LibertyUserController::class, 'established']);
    Route::post('established/liberty-user', [LibertyUserController::class, 'established']);
    Route::post('established/broker-user', [BrokerUserController::class, 'established']);
    Route::post('established/external-surveyor', [ExternalSurveyorController::class, 'established']);
    Route::post('established/client', [UserController::class, 'established']);

    Route::post('liberty-users/activate', [LibertyUserController::class, 'activate']); /* completed */
    Route::post('liberty-users/get-previsico-ui-notif',
        [LibertyUserController::class, 'getPrevisicoUiNotif']); /* completed */
    Route::post('liberty-users/clear-previsico-ui-notif',
        [LibertyUserController::class, 'clearPrevisicoUiNotif']); /* completed */
    Route::post('client-user/get-previsico-ui-notif',
        [UserController::class, 'getPrevisicoUiNotif']);
    Route::post('client-user/clear-previsico-ui-notif',
        [UserController::class, 'clearPrevisicoUiNotif']);
    Route::post('external-surveyors/activate',
        [ExternalSurveyorController::class, 'activate']);
    Route::post('broker-users/activate', [BrokerUserController::class, 'activate']);
    Route::post('email-hook', [VideoCallController::class, 'receiveEmail']);
    Route::get('get-lib-rep/{room_id}',
        [VideoCallController::class, 'primaryLibRepFromRoomId']);


    Route::post('liberty-users/get-multiple-user-details',
        [LibertyUserController::class, 'getMultipleUserDetails']); 
    Route::post('client-users/get-multiple-user-details',
        [UserController::class, 'getMultipleUserDetails']); 

    Route::get('liberty-users/account-engineers',
        [LibertyUserController::class, 'getAllAccountEngineers']); 


    // Organisations
    Route::group(['prefix' => 'rhs-organisations'], function () {
        Route::post('/', [RHSOrganisationController::class, 'store']);
        Route::get('/name/{organisation_id}',
            [RHSOrganisationController::class, 'getOrganisationName']);
        Route::get('/test', [TestController::class, 'test']);
        Route::post('/document/info',
            [RHSOrganisationController::class, 'getDocumentInformation']);
    });

    // Cyber VR
    Route::group(['prefix' => 'cyber-vr'], function () {
        Route::get('/theme-rooms/{personId}', [CyberGuestController::class, 'index']);
        Route::get('/theme-rooms/{roomCode}/{personId}', [CyberGuestController::class, 'space']);
    });

    // CMS
    Route::group(['prefix' => 'cms'], function () {
        Route::get('/get-office/{id}', [CmsController::class, 'getOfficeDetails']);
    });

    // GDPR and Data Privacy routes
    // This will handle signed route from email
    Route::group(['prefix' => 'gdpr', 'as' => 'gdpr.'], function () {
        Route::group(['prefix' => 'data-privacy', 'as' => 'data-privacy.'], function () {
            Route::get('/{action}', [App\Http\Controllers\Api\V1\GDPR\DataPrivacyController::class, 'action'])
                ->where('action', 'delete|anonymise')
                ->name('action')
                ->middleware('signed');
        });
    });
});

Route::group(['prefix' => 'v1', 'middleware' => 'auth'], function () {
    //TEST ROUTE
    //Route::get('add_tp_to_org',[App\Http\Controllers\Api\V1\RHSOrganisationController::class, 'add_tp_to_org']);
    // log

    Route::post('register-notification-token',
        [UserController::class, 'registerNotificationToken']);
    //Forum
    Route::group(['prefix' => 'community/'], function () {

        Route::get('get-sector', [SectorController::class, 'getSector']); /* completed */
        Route::get('get-community-tag', [TagController::class, 'getCommunityTag']); /* completed */
        Route::get('get-popular-community-tag', [TagController::class, 'getPopularCommunityTag']); /* completed */
        Route::get('refresh-tags', [TagController::class, 'refresh']); /* completed */

        Route::group(['prefix' => 'messages/'], function () {
            Route::get('upcoming/{id}/{type?}/{limit?}', [MessageController::class, 'getUpcomingEvent']); /* completed */
            Route::get(
                'get-popular-event/{type}/{subscription}/{user_type}',
                [MessageController::class, 'getPopularEvent']
            ); /* completed */

            Route::post('store', [MessageController::class, 'store']); /* completed */
            Route::post('vote', [MessageController::class, 'vote']); /* completed */

            Route::get('', [MessageController::class, 'index']); /* completed */
            Route::post('all', [MessageController::class, 'all']); /* completed */
            Route::get('{id}', [MessageController::class, 'show']); /* completed */

            Route::put('{id}/update', [MessageController::class, 'update']); /* completed */
            Route::delete('{id}/delete', [MessageController::class, 'delete']); /* completed */

            Route::put('{id}/interested', [MessageController::class, 'interested']); /* completed */

            Route::put('{id}/reply/{reply_id}/like', [MessageController::class, 'like']); /* completed */

            Route::group(['prefix' => 'file/'], function () {
                Route::post('upload', [FileController::class, 'upload']); /* completed */
                Route::post('download', [FileController::class, 'download']); /* completed */
                Route::get('{id}', [FileController::class, 'link']); /* completed */
                Route::delete('{id}/delete', [FileController::class, 'delete']); /* completed */
            });

            Route::group(['prefix' => 'reply/'], function () {
                Route::post('store', [ReplyController::class, 'store']); /* completed */
                Route::get('{id}', [ReplyController::class, 'show']); /* completed */
                Route::put('{id}/update', [ReplyController::class, 'update']); /* completed */
                Route::delete('{id}/delete', [ReplyController::class, 'delete']); /* completed */

            });
        });

        Route::group(['prefix' => 'sectors'], function () {
            Route::post('subscribe', [SectorController::class, 'subscribe']); /* completed */
            Route::post('unsubscribe', [SectorController::class, 'unsubscribe']); /* completed */
            Route::post('organisation-subscribe', [SectorController::class, 'orgSubscribe']); /* completed */
            Route::post('organisation-unsubscribe', [SectorController::class, 'orgUnsubscribe']); /* completed */
            Route::get('subscriptions/user/{id}/{type?}', [SectorController::class, 'getUserSubscription']); /* completed */
            Route::get(
                'subscriptions/organisation/{id}',
                [SectorController::class, 'getOrganisationSubscription']
            ); /* completed */

            Route::get('deleted', [MessageController::class, 'getOrphanedSectors']); /* completed */
        });
    });

    Route::group(['prefix' => 'client-dashboard'], function () {
        Route::post('component/order',
            [
                App\Http\Controllers\Api\V1\ClientDashboardController::class,
                'storeClientComponentOrder',
            ]); /* completed */
            
        Route::post('component/collapse', [
            App\Http\Controllers\Api\V1\ClientDashboardController::class,
            'storeComponentCollapseSettings',
        ]);

        Route::post('update-last-login',
            [App\Http\Controllers\Api\V1\ClientDashboardController::class, 'updateLastLoginAt']); /* completed */
        Route::post('update-last-seen-at',
            [App\Http\Controllers\Api\V1\ClientDashboardController::class, 'updateLastSeenAt']); /* completed */

        Route::group(['prefix' => 'charts'], function () {
            Route::get('status', [App\Http\Controllers\Api\V1\ClientDashboardController::class, 'getStatus']); 
        });
    });

    Route::group(['prefix' => 'rhs'], function () {
        Route::group(['prefix' => 'get'], function () {
            Route::get('/customer/order/{order_id}',
                [CustomerController::class, 'getCustomerOrder']);
        });
        /** completed */

        Route::group(['prefix' => 'submit'], function () {
            Route::get('/customer/order/{order_data}',
                [CustomerController::class, 'createCustomerOrder']);
        });
        /** completed */

        // Route::get('products', [ProductController::class, 'index']);
        Route::get('products/categories', [ProductController::class, 'categories']);
        /** completed */
        Route::get('products/descriptions', [ProductController::class, 'descriptions']);
        /** completed */
        Route::get('products', [ProductController::class, 'products']);
        /** completed */
        Route::get('orders', [OrderController::class, 'index']);
        /** completed */

        Route::group(['prefix' => 'customers'], function () {
            Route::get('/', [CustomerController::class, 'index']);
            /** completed */
            Route::get('/{customer}', [CustomerController::class, 'show']);
            /** completed */
            Route::put('/{customer}', [CustomerController::class, 'update']);
            /** completed */

        });

        Route::group(['prefix' => 'organisations'], function () {
            Route::get('/', [RHSOrganisationController::class, 'all']);
            /** completed */
            Route::get('/{customer}', [RHSOrganisationController::class, 'show']);
            /** completed */
        });
    });

    Route::group(['prefix' => 'rrappetite'], function () { //MORRIS
        Route::post('auth', [AuthController::class, 'auth']);
        /** completed */
        Route::get('index', [IndexController::class, 'index']);
        /** completed */
        Route::group(['prefix' => 'regions'], function () {
            Route::get('/', [RegionController::class, 'index']);
            /** completed */
            Route::get('/slug/{slug}',
                [RegionController::class, 'regionBySlug']);
            /** completed */
            Route::get('/{region}', [RegionController::class, 'show']);
            /** completed */
            Route::get('/{id}/info',
                [RegionController::class, 'getRegionSectors']);
            /** completed */
            /** TODO: method getStock does not exist */
            Route::get('/{id}/stock', [RegionController::class, 'getStock']);
            Route::post('/{id}/subsectors',
                [RegionController::class, 'getRegionSubsectors']);
            /** completed */
            Route::post('/{id}/products',
                [RegionController::class, 'getRegionProducts']);
            /** completed */
            Route::post('/{id}/products/info',
                [RegionController::class, 'getRegionProductsInfo']);
            /** completed */
            Route::post('/products/info',
                [RegionController::class, 'getOrphanProduct']);
            /** completed */
        });
        Route::group(['prefix' => 'siccodes'], function () {
            Route::get('/', [SicCodeController::class, 'index']);
            /** completed */
            Route::get('/sector/{id}',
                [SicCodeController::class, 'siccodesBysectorId']);
            /** completed */
            Route::get('/subsector/{id}',
                [SicCodeController::class, 'siccodesBysubsectorId']);
            /** completed */
        });
        Route::group(['prefix' => 'sectors'], function () {
            Route::get('/', [RRAppetiteSectorController::class, 'index']);
            /** completed */
            Route::get('/all', [RRAppetiteSectorController::class, 'all']);
            /** completed */
            Route::get('/{id}', [RRAppetiteSectorController::class, 'show']);
            /** completed */
            Route::post('/slug', [RRAppetiteSectorController::class, 'getBySlug']);
            /** completed */
            Route::post('/store', [RRAppetiteSectorController::class, 'store']);
            /** completed */
            Route::post('/{id}/update', [RRAppetiteSectorController::class, 'update']);
            /** completed */
            Route::delete('/{id}/delete', [RRAppetiteSectorController::class, 'delete']);
            /** completed */
        });
        Route::group(['prefix' => 'subsectors'], function () {
            Route::get('/', [SubsectorController::class, 'index']);
            /** completed */
            Route::get('/all', [SubsectorController::class, 'all']);
            /** completed */
            Route::get('/{id}', [SubsectorController::class, 'show']);
            /** completed */
            Route::get('/tags/{id}',
                [SubsectorController::class, 'getSubsectorTagsBySubsectorID']);
            /** completed */
            Route::post('/slug', [SubsectorController::class, 'getBySlug']);
            /** completed */
            Route::post('/store', [SubsectorController::class, 'store']);
            /** completed */
            Route::post('/{id}/update', [SubsectorController::class, 'update']);
            /** completed */
            Route::delete('/{id}/delete',
                [SubsectorController::class, 'delete']);
            /** completed */
        });
        Route::group(['prefix' => 'products'], function () {
            Route::get('/{id}/details',
                [RRAppetiteProductController::class, 'getProductAssociations']);
            /** completed */
            Route::get('/all', [RRAppetiteProductController::class, 'all']);
            /** completed */
            Route::get('/search', [RRAppetiteProductController::class, 'search']);
            /** completed */
            Route::get('/searchall', [RRAppetiteProductController::class, 'searchAll']);
            /** completed */
            Route::get('/{id}', [RRAppetiteProductController::class, 'show']);
            /** completed */
            Route::post('/slug', [RRAppetiteProductController::class, 'getBySlug']);
            /** completed */
            Route::post('/underwriter',
                [RRAppetiteProductController::class, 'getUnderwriterForProduct']);
            /** completed */
            Route::post('/store', [RRAppetiteProductController::class, 'store']);
            /** completed */
            Route::post('/{product_id}/update',
                [RRAppetiteProductController::class, 'update']);
            /** completed */
            Route::post('/{product_slug}/{sector_slug}/search',
                [RRAppetiteProductController::class, 'searchProduct']);
            /** completed */
            Route::delete('/{id}/delete', [RRAppetiteProductController::class, 'delete']);
            /** completed */
            Route::post('/relationships/delete',
                [RRAppetiteProductController::class, 'destroyProductCombination']);
            /** completed */
            Route::delete('/{id}/delete-video-frame',
                [RRAppetiteProductController::class, 'deleteVideoFrame']);
            /** completed */
        });
        Route::group(['prefix' => 'form'], function () {
            Route::get('/all', [FormController::class, 'all']);
            Route::get('/all/active', [FormController::class, 'activeForms']);
            Route::get('/all/archive', [FormController::class, 'archivedForms']);
            
            Route::get('/{formSubmission}', [FormController::class, 'show']);
            Route::get('/ics/{formSubmission}',
                [FormController::class, 'icsString']);
            Route::post('/submit', [FormController::class, 'handleSubmission']);
            Route::post('/{id}/archive',
                [FormController::class, 'changeFormArchiveStatus']);
            Route::post('/{id}/enquiry',
                [FormController::class, 'changeFormEnquiryStatus']);
            Route::get('/{id}/resend-broker/{verified?}',
                [FormController::class, 'resendBrokerEmail']);
        });


        Route::group(['prefix' => 'new-form'], function () {
            Route::get('/all/active', [NewFormController::class, 'activeForms']);
            /** completed */
            Route::get('/all/archive',
                [NewFormController::class, 'archivedForms']);
            /** completed */
            Route::get('/{formSubmission}', [NewFormController::class, 'show']);
            /** completed */
            Route::get('/ics/{formSubmission}',
                [NewFormController::class, 'icsString']);
            /** completed */
            Route::post('/submit',
                [NewFormController::class, 'handleSubmission']);
            /** completed */
            Route::get('/{id}/resend-broker/{verified?}',
                [NewFormController::class, 'resendBrokerEmail']);
            /** completed */
        });
    });

    Route::group(['prefix' => 'log', 'before' => 'auth.liberty-user'], function () {
        Route::post('add', [LogController::class, 'add']);
        Route::post('/overview', [LogController::class, 'overview']);
        Route::post('/pages', [LogController::class, 'page']);
        Route::post('/activations', [LogController::class, 'activations']);
    });

    // Liberty Users
    Route::group(['prefix' => 'liberty-users', 'before' => 'auth.liberty-user'], function () {

        //GET
        Route::get('all', [LibertyUserController::class, 'all']);
        Route::get('all-filtered', [LibertyUserController::class, 'allFiltered']);

        /** completed */

        //POST
        Route::post('auth', [LibertyUserController::class, 'auth']);
        Route::get('auth/{email}', [LibertyUserController::class, 'tokenAuth']);
        /** completed */
        Route::post('store', [LibertyUserController::class, 'store']); /* completed */
        Route::post('update', [LibertyUserController::class, 'update']); /* completed */
        Route::post('send-reset-password-code',
            [LibertyUserController::class, 'sendResetPasswordCode']); /* completed */
        Route::post('reset-password', [LibertyUserController::class, 'resetPassword']); /* completed */
        Route::post('mobile/auth', [LibertyUserController::class, 'mobileauth']); /* completed */
    });

    // DMS Users
    Route::group(['prefix' => 'dms-users', 'before' => 'auth.liberty-user'], function () {
        Route::post('mobile/auth', [LibertyUserController::class, 'mobileauth']);
    });

    // External Surveyors
    Route::group(['prefix' => 'external-surveyors', 'before' => 'auth.external-surveyor'], function () {

        //GET
        Route::get('all', [ExternalSurveyorController::class, 'all']);
        /** completed */

        //POST
        Route::post('auth', [ExternalSurveyorController::class, 'auth']);
        Route::post('mobile/auth', [ExternalSurveyorController::class, 'mobileauth']);
        Route::post('store', [ExternalSurveyorController::class, 'store']);
        Route::post('update', [ExternalSurveyorController::class, 'update']);
        Route::post('send-reset-password-code',
            [ExternalSurveyorController::class, 'sendResetPasswordCode']);
        Route::post('reset-password', [ExternalSurveyorController::class, 'resetPassword']);
        Route::get('link/{id}', [ExternalSurveyorController::class, 'generateLink']);
    });

    // Broker Users
    /* completed */
    Route::group(['prefix' => 'broker-users', 'before' => 'auth.broker-user'], function () {
        Route::get('orgs/{broker_id}', [BrokerUserController::class, 'orgsForBrokerUser']);
        Route::get('open-market/{broker_id}',
            [BrokerUserController::class, 'openMarketForBrokerUser']);
        Route::get('organisation/options/{id}',
            [BrokerUserController::class, 'organisationOptions']);
        Route::post('auth', [BrokerUserController::class, 'auth']);
        Route::post('send-reset-password-code',
            [BrokerUserController::class, 'sendResetPasswordCode']);
        Route::post('reset-password', [BrokerUserController::class, 'resetPassword']);
        Route::get('link/{id}', [BrokerUserController::class, 'generateLink']);
    });

    Route::group(['prefix' => 'broker-users'], function () {
        Route::get('all/{page?}/{limit?}', [BrokerUserController::class, 'all']);
        Route::get('get-data-for-datatable', [BrokerUserController::class, 'getDataForDataTable']);
        Route::get('{email}/reset-login-attempts', [BrokerUserController::class, 'resetLoginAttempts']);
    });
    /* end Broker Users */

    Route::group(['prefix' => 'cms-products'], function () {
        Route::get('all-products', [CMSProductController::class, 'getAllProducts']);
        Route::get('region/{region}/sector/{sector}/subsector/{subsector}',
            [CMSProductController::class, 'ProductList']);
        Route::get('region/{region}/sector/{sector}/subsector/{subsector}/product/{product}',
            [CMSProductController::class, 'prepareProductJson']);
        Route::post('download-product', [CMSProductController::class, 'downloadProduct']);
    });

    Route::group(['prefix' => 'cms-sectors'], function () {
        Route::get('all-sectors', [SectorSubsectorController::class, 'sectorsList']);
        Route::get('{sector}/subsectors',
            [SectorSubsectorController::class, 'subSectorsList']);
    });

    // @NOTE: Decommissioned
    // Route::group(['prefix' => 'cms-virtual-rooms'], function () {
    //     Route::get('options', [VirtualRoomsOptionsController::class, 'options']);
    // });

    Route::resource('broker-users', 'App\Http\Controllers\Api\V1\BrokerUserController',
        ['only' => ['store', 'update', 'show', 'destroy']]);

    // MGA Schemes
    Route::group(['prefix' => 'mga-schemes'], function () {
        Route::get('all/{page?}/{limit?}', [MgaSchemeController::class, 'all']);
        Route::get('has-mga-schemes/{id}', [MgaSchemeController::class, 'hasMgaSchemes']);
    });

    Route::resource('mga-schemes', 'App\Http\Controllers\Api\V1\MgaSchemeController',
        ['only' => ['index', 'store', 'update', 'show', 'destroy']]);

    // Trade Groups
    Route::group(['prefix' => 'trade-groupings'], function () {
        Route::get('options', [TradeGroupsController::class, 'options']);
        Route::get('{id}/default', [TradeGroupsController::class, 'defaultTradeGroup']);
    });
    Route::resource('trade-groupings', 'App\Http\Controllers\Api\V1\TradeGroupsController');

    // Trades
    Route::group(['prefix' => 'trades'], function () {
        Route::get('options/{id?}', [TradesController::class, 'options']);
    });
    Route::resource('trades', 'App\Http\Controllers\Api\V1\TradesController');

    // Users
    Route::group(['prefix' => 'user'], function () {

        Route::get('reset-login/{id?}', [UserController::class, 'resetLogin']);
        Route::get('reset-login-external/{id?}',
            [UserController::class, 'resetLoginExternalUser']);
        Route::get('reset-login-attempts/{id?}', [UserController::class, 'resetLoginAttempts']);

        // GET
        Route::get('branch-users/{id?}', [UserController::class, 'branchUsers']); /* completed */
        Route::get('broker/{id}/{page?}/{limit?}', [UserController::class, 'broker']); /* completed */
        Route::get('all/{page?}/{limit?}', [UserController::class, 'all']); /* completed */
        Route::get('all-filtered', [UserController::class, 'allFiltered']); /* completed */
        Route::get('find/{id}', [UserController::class, 'find']);  /* completed */
        Route::get('organisation/{id}', [UserController::class, 'findUsersByOrganisation']); /* completed */
        Route::get('org-branch/{id}',
            [UserController::class, 'findBranchesByOrganisation']); /* completed */
        Route::get('send-welcome/{id}', [UserController::class, 'sendWelcome']); /* completed */
        Route::get('/licenses', [UserController::class, 'licenses']); /* completed */
        Route::get('userorgdetails/{id}', [UserController::class, 'findUserOrg']); /* completed */
        Route::get('link/{id}', [UserController::class, 'generateLink']); /* completed */
        Route::get('check-welcome-modal-viewed/{id}', [UserController::class, 'checkWelcomeModalViewed']); /* completed */
        Route::get('check-tour-modal-viewed/{id}', [UserController::class, 'updateTourModalViewed']);
        Route::get('get-tour-modal-viewed/{id}', [UserController::class, 'getTourModalViewed']); 

        // POST
        Route::post('auth', [UserController::class, 'auth']); /* completed */

        Route::post('register', [UserController::class, 'register']);
        Route::post('store', [UserController::class, 'store']);
        Route::post('update', [UserController::class, 'update']);
        Route::post('send-reset-password-code',
            [UserController::class, 'sendResetPasswordCode']);
        Route::post('reset-password', [UserController::class, 'resetPassword']);
        Route::post('activate', [UserController::class, 'activate']);
        Route::post('updatepassword', [UserController::class, 'change_password']);
        Route::post('delete', [UserController::class, 'destroy']);
    });

    // Liberty Users (admin)
    Route::group(['prefix' => 'liberty-users'], function () {
        //GET
        Route::get('allLibertyUsers', [LibertyUserController::class, 'allLibertyUsers']); /* completed */
        Route::get('all/{page?}/{limit?}/{role?}/{order?}',
            [LibertyUserController::class, 'all']); /* completed */

        Route::get('send-welcome/{id}', [LibertyUserController::class, 'sendWelcome']);
        Route::get('options/{role?}', [LibertyUserController::class, 'options']); /* completed */
        Route::get('options-multiple/', [LibertyUserController::class, 'optionsMultiple']); /* completed */
        Route::get('link/{id}', [LibertyUserController::class, 'generateLink']); /* completed */

        //POST
        Route::post('send-reset-password-code',
            [LibertyUserController::class, 'sendResetPasswordCode']); /* completed */
        Route::post('reset-password', [LibertyUserController::class, 'resetPassword']);  /* completed */
        Route::post('updatepassword', [LibertyUserController::class, 'change_password']);
    });
    Route::resource('liberty-users', 'App\Http\Controllers\Api\V1\LibertyUserController',
        ['only' => ['store', 'update', 'show', 'destroy']]); /* completed */

    // Liberty Branches
    Route::group(['prefix' => 'liberty-branches'], function () {
        //GET
        Route::get('all/{page?}/{limit?}', [BranchController::class, 'all']);
        Route::get('options', [BranchController::class, 'options']);
        Route::get('options/aspen-branches', [BranchController::class, 'optionsAspen']);
    });

    Route::resource('liberty-branches', 'App\Http\Controllers\Api\V1\BranchController',
        ['only' => ['store', 'update', 'show', 'destroy']]);

    Route::get('themes/options', [OrganisationController::class, 'themeOptions']);

    // External Surveyors
    Route::group(['prefix' => 'external-surveyors'], function () {
        //GET
        Route::get('all/{page?}/{limit?}/{companyId?}', [ExternalSurveyorController::class, 'all']);
        /** completed */
        Route::get('send-welcome/{id}', [ExternalSurveyorController::class, 'sendWelcome']);
        Route::get('options/{role?}', [ExternalSurveyorController::class, 'options']);
        /** completed */

        //POST
        Route::post('send-reset-password-code',
            [ExternalSurveyorController::class, 'sendResetPasswordCode']);
        Route::post('reset-password', [ExternalSurveyorController::class, 'resetPassword']);
        Route::post('updatepassword',
            [ExternalSurveyorController::class, 'change_password']);
    });
    Route::resource('external-surveyors', 'App\Http\Controllers\Api\V1\ExternalSurveyorController',
        ['only' => ['store', 'update', 'show', 'destroy']]);

    // External Survey Companies
    Route::group(['prefix' => 'external-survey-companies'], function () {
        //GET
        Route::get('all/{page?}/{limit?}', [ExternalSurveyCompanyController::class, 'all']);
        Route::get('options', [ExternalSurveyCompanyController::class, 'options']);
        Route::get('{id}/surveyors', [ExternalSurveyCompanyController::class, 'surveyors']);
    });

    Route::resource('external-survey-companies', 'App\Http\Controllers\Api\V1\ExternalSurveyCompanyController',
        ['only' => ['store', 'update', 'show', 'destroy']]);

    // Brokers
    /** completed */
    Route::group(['prefix' => 'brokers'], function () {
        //GET
        Route::get('all/{page?}/{limit?}/{broker_org?}', [BrokerController::class, 'all']);
        Route::get('options', [BrokerController::class, 'options']);
    });

    Route::resource('brokers', 'App\Http\Controllers\Api\V1\BrokerController',
        ['only' => ['store', 'update', 'show', 'destroy']]);
    /** end completed */

    //Search
    Route::group(['prefix' => 'search'], function () {

        //POST
        Route::post('/', [SearchController::class, 'search']);

    });

    // Accident reporting chart data
    Route::group(['prefix' => 'reports'], function () {
        Route::get('accidents_by_time_of_occurence/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'accidents_by_time_of_occurence']); /* completed */
        Route::get('where_accident_happened/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'where_accident_happened']); /* completed */
        Route::get('injured_person_type/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'injured_person_type']); /* completed */
        Route::get('accident_severity/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'accident_severity']); /* completed */
        Route::get('accident_category/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'accident_category']); /* completed */
        Route::get('action_taken/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'action_taken']); /* completed */
        Route::get('nature_of_injury/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'nature_of_injury']); /* completed */
        Route::get('number_of_accidents_by_branch/{start}/{end}/{org}/{claims?}/{sector?}',
            [AccidentReportingController::class, 'number_of_accidents_by_branch']); /* completed */
    });

    // Account Documents
    /* completed */
    Route::group(['prefix' => 'account-documents'], function () {
        Route::get('all', [AccountDocumentController::class, 'all']);
        Route::get('onboarding/{organisationId}',
            [AccountDocumentController::class, 'onboarding']);
        Route::post('/', [AccountDocumentController::class, 'store']);
        Route::post('{id}/destroy', [AccountDocumentController::class, 'destroy']);
        Route::get('{id}', [AccountDocumentController::class, 'show']);
        Route::post('{id}', [AccountDocumentController::class, 'update']);
    });
    Route::get('account-document-types', [AccountDocumentController::class, 'types']);
    /* end completed */

    //Helpers
    Route::group(['prefix' => 'helper-api'], function () {
        Route::get('/survey-details-for-clean-up/{flag}', [APIControllerForCommand::class, 'getSurveyDetailsForCleanUp']);
        Route::post('/insert-srg-close-log', [APIControllerForCommand::class, 'insertSRGLogData']);
        Route::get('/srg/get-srg-close-log', [APIControllerForCommand::class, 'getSRGLogData']);
    });

    //Organisations
    Route::group(['prefix' => 'organisation'], function () {
        // GET
        Route::get('broker/{id}/{page?}/{limit?}',
            [OrganisationController::class, 'broker']);
        Route::get('all/{page?}/{limit?}', [OrganisationController::class, 'all']);
        Route::get('options/broker/{id}', [OrganisationController::class, 'brokerOptions']);
        Route::get('options', [OrganisationController::class, 'options']);
        Route::get('options/broker-user/{id}',
            [OrganisationController::class, 'brokerUsersOptions']);
        Route::get('options/get-broker-user/{mga_id}/{broker_org_id}',
            [OrganisationController::class, 'getBrokerUserByMGA']);

        Route::get('/get-organisation-with-surveys-and-risk-engineer-type', [OrganisationController::class, 'getOrgWitSurveysOrRiskEngineerType']);
        Route::get('{id}', [OrganisationController::class, 'find']);
        Route::get('{id}/info/{userId?}', [OrganisationController::class, 'orgInfo']);
        Route::get('{id}/org-info', [OrganisationController::class, 'organisationInfo']);
        Route::get('{id}/policy-numbers',
            [OrganisationController::class, 'showPolicyNumbers']);
        Route::get('{id}/claims', [OrganisationClaimController::class, 'all']);
        Route::get('{id}/contacts', [OrganisationContactController::class, 'all']);
        Route::get('{id}/overview', [OrganisationOverviewController::class, 'show']);

        Route::get('/dms/dms-organisations',
            [DocumentManagementController::class, 'GetAllOrganisations']);
        Route::get('dms/{id}',
            [DocumentManagementController::class, 'GetOrganisationDetailsForDMS']);
        Route::get('/risk-gradings/{id}', [OrganisationController::class, 'riskGradings']);
        Route::get('/get-users-with-max-surveys/{id}', [OrganisationController::class, 'getOrgUsersWithMaxSurveys']);

       

        // POST
        Route::post('store', [OrganisationController::class, 'store']);
        Route::post('update', [OrganisationController::class, 'update']);
        Route::post('delete', [OrganisationController::class, 'delete']);
        Route::post('{organisation_id}/delete-sov',
            [OrganisationController::class, 'deleteSov']);
        Route::post('{id}/overview', [OrganisationOverviewController::class, 'save']);

        // Reports
        Route::group(['prefix' => '{organisation_id}/reports'], function () {
            Route::get('all', [OrganisationReportController::class, 'all']);
            Route::post('/', [OrganisationReportController::class, 'store']);
            Route::post('{id}/destroy', [OrganisationReportController::class, 'destroy']);
            Route::get('{id}', [OrganisationReportController::class, 'show']);
            Route::post('{id}', [OrganisationReportController::class, 'update']);
        });


        // Notes
        Route::group(['prefix' => '{organisation_id}/notes'], function () {
            Route::get('/', [OrganisationNoteController::class, 'all']);
            Route::post('/', [OrganisationNoteController::class, 'store']);
            Route::get('{note_id}', [OrganisationNoteController::class, 'show']);
            Route::post('{note_id}/update', [OrganisationNoteController::class, 'update']);
            Route::post('{note_id}/destroy',
                [OrganisationNoteController::class, 'destroy']);
        });

        // Flood Alerts
        Route::get('{organisation_id}/flood-alerts/{userId?}',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'index']);

        Route::get('{organisation_id}/all-flood-alerts/{userId?}',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'allFloodAlerts']);

        Route::get('{organisation_id}/flood-alerts/initial-past-alerts/{userId?}',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'initialPastAlerts']);

        Route::get('{organisation_id}/flood-alerts/past-alerts/{userId}/{months}/{location?}',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'pastAlerts']);

        Route::post('flood-alerts/component/order',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'storeClientComponentOrder']);
        
        Route::get('{organisation_id}/flood-alerts-locations',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'floodAlertLocations']);

        Route::post('flood-alerts/component/order',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'storeClientComponentOrder']);

        Route::get('/flood-alerts/{id}',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'getAlert']);
        Route::post('/flood-alert/actioned',
        [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'actioned']);

        Route::get('/keyaccounts/export', [OrganisationKeyAccountController::class, 'exportKeyAccounts']);

        // Risk Engineer Productivity Metrics
        Route::get('/{id}/re-bound-status', [OrganisationController::class, 'getBoundStatus']);
        Route::get('/{id}/renewals/{startDate?}/{endDate?}', [OrganisationController::class, 'getRenewals']);
        Route::get('/{id}/organisation-policy-logs', [OrganisationPolicyLogController::class, 'index']);
        Route::get('{id}/risk-engineer-productivity-metrics/filter', [OrganisationController::class, 'getRiskEngineerProductivityMetrics']);

        Route::get('/all-without-appends', [OrganisationController::class, 'getAllOrganisations']);
    });

    Route::group(['prefix' => 'organisation-contact'], function () {
        Route::get('/getAllRiskEngineer', [OrganisationContactController::class, 'getAllRiskEngineer']);
        Route::get('/getAllRiskEngineerOptions', [OrganisationContactController::class, 'getAllRiskEngineerForOptions']);
        Route::get('/getAllUnderwriterOptions', [OrganisationContactController::class, 'getAllUnderwriterForOptions']);
    });

    // Organisation Settings
    Route::group(['prefix' => 'organisation-settings'], function () {
        Route::post('/storeOrUpdate', [OrganisationSectionSettingController::class, 'storeOrUpdate']);
        Route::get('/settings/{organisation_id}', [OrganisationSectionSettingController::class, 'getSettings']);
    });

    //Policy Numbers
    Route::group(['prefix' => 'policy-types'], function () {

        Route::Get('all/{page?}/{limit?}', [App\Http\Controllers\Api\V1\PolicyTypeController::class, 'all']);
        Route::get('/organisation/{organisation_id}',
            [App\Http\Controllers\Api\V1\PolicyTypeController::class, 'organisation']);
        Route::get('options/{column_name?}', [App\Http\Controllers\Api\V1\PolicyTypeController::class, 'options']);

    });

    Route::get('organisation_name/{id}', [OrganisationController::class, 'findOrg']);

    //Claim Types
    Route::group(['prefix' => 'claim-types'], function () {

        //GET
        Route::get('all/{sector}', [App\Http\Controllers\Api\V1\ClaimTypeController::class, 'all']);

        //POST

    });

    //Sectors
    Route::group(['prefix' => 'sector'], function () {

        //GET
        Route::get('all', [App\Http\Controllers\Api\V1\SectorController::class, 'all']);
        Route::get('options', [App\Http\Controllers\Api\V1\SectorController::class, 'options']);

        //POST

    });

    //Cover
    Route::group(['prefix' => 'cover'], function () {
        //GET
        Route::get('all', [App\Http\Controllers\Api\V1\CoverController::class, 'all']);
        Route::get('options', [App\Http\Controllers\Api\V1\CoverController::class, 'options']);
    });

    //Cover
    Route::group(['prefix' => 'doc_level'], function () {
        //GET
        Route::get('{org_id}/{level}/{parent?}/{sector?}',
            [App\Http\Controllers\Api\V1\DocumentLevelController::class, 'getLevel']);
        Route::get('/getUpcomingLevelDetails/{organisation_id}/{sector}/{level1}/{level2}/{level3}',
            [App\Http\Controllers\Api\V1\DocumentLevelController::class, 'getSubLevelContentCount']);
        Route::get('/faspido/getDocForLevel1And4/{organisation_id}/{sector_id}/{level1}',
            [App\Http\Controllers\Api\V1\DocumentLevelController::class, 'getDocForLevelType1And4']);

        //POST
        Route::post('/all_docs',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'findDocumentsPerOrganisationLevels']);
        Route::post('/featured', [App\Http\Controllers\Api\V1\DocumentController::class, 'featured']);

    });
    //GET
    Route::get('doc_levels_all/{level1}/{level2?}/{level3?}/{level4?}',
        [App\Http\Controllers\Api\V1\DocumentLevelController::class, 'getLevelsAll']);

    //Accident Reports
    Route::get('/form/accident_report/organisation/{id}/{page?}/{limit?}/{fewfields?}',
        [App\Http\Controllers\Api\V1\FormController::class, 'accidentReportingFormsForOrganisation']);
    Route::get('/form/submitted_forms/accident_report/{org_id}/{user_id}/{manager}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'submittedAccidentFormsForOrg']); /** completed */
    Route::get('/form/submitted_forms/accident_claims/{org_id?}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'submittedAccidentFormsClaims']); /** completed */
    Route::get('/form/saved_forms/accident_report/{org_id}/{user_id}/{manager}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'savedAccidentFormsForOrg']); /** completed */

    //D&O
    Route::group(['prefix' => 'do-facilities'], function () {
        Route::resource('submission-links', 'App\Http\Controllers\Api\V1\OrganisationSubmissionLinksController');
        Route::post('submission-links/{id}/revoke',
            [App\Http\Controllers\Api\V1\OrganisationSubmissionLinksController::class, 'revoke']);
        Route::resource('pending-submissions', 'App\Http\Controllers\Api\V1\OrganisationSubmissionsController');
        Route::post('pending-submissions/{id}/status/{status}',
            [App\Http\Controllers\Api\V1\OrganisationSubmissionsController::class, 'status']);
        Route::post('upload-borderau', [App\Http\Controllers\Api\V1\UploadBorderauController::class, 'store']);
    });

    //Import Bordereau
    /** completed */
    Route::group(['prefix' => 'bordereau'], function () {
        Route::post('upload', [App\Http\Controllers\Api\V1\OrganisationBordereauController::class, 'store']);
        Route::get('all', [App\Http\Controllers\Api\V1\OrganisationBordereauController::class, 'all']);
        Route::post('view', [App\Http\Controllers\Api\V1\OrganisationBordereauController::class, 'show']);
        Route::post('reject', [App\Http\Controllers\Api\V1\OrganisationBordereauController::class, 'reject']);
        Route::post('approve', [App\Http\Controllers\Api\V1\OrganisationBordereauController::class, 'approve']);
    });
    /** end completed */

    //Import Locations
    /** completed */
    Route::group(['prefix' => 'location'], function () {
        Route::post('risk-grading-logs',
            [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'riskGradingLogs']);
        Route::post('risk-grading-logs-overview',
            [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'riskGradingLogOverview']);
        Route::post('upload', [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'store']);
        Route::get('{organisation_id}',
            [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'getLocation']);
        Route::get('show/{id}', [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'getLocationData']);
        Route::put('update', [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'update']);
        Route::post('delete/{id}', [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'delete']);
        Route::get('{location_id}/flood-alerts',
            [App\Http\Controllers\Api\V1\Previsico\FloodAlertsController::class, 'show']);

        Route::post('bulk-match-locations/{organisation_id}', [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'bulkMatchLocations']);
        Route::post('bulk-upload-locations/{organisation_id}', [App\Http\Controllers\Api\V1\OrganisationLocationController::class, 'bulkUploadLocations']);
    });
    /** end completed */

    Route::get('form/public/get-all-forms',
    [App\Http\Controllers\Api\V1\PublicFormController::class, 'getAllPublicForms']);
    Route::get('form/private/get-all-forms',
    [App\Http\Controllers\Api\V1\FormController::class, 'getAllPrivateForms']);

    // Public forms
    Route::get('form/public/export-submissions/{id}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'exportSubmissions']); /** completed */
    Route::get('form/public/submissions/{id}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'submissions']); /** completed */
    Route::get('form/public/submissions/{id}/index',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'index']); /** completed */
    Route::get('form/public/submission/{id}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'show']); /** completed */
    Route::delete('form/public/submission/{submission}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'destroy']); /** completed */
    Route::get('form/public/submission/form/{uuid}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'form']); /** completed */
    Route::post('form/public/submissions',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'store']); /** completed */
    Route::post('form/public/submissions/{submission_id}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'modify']); /** completed */
    Route::get('form/public/org-links/{id}', [App\Http\Controllers\Api\V1\PublicFormLinkController::class, 'index']); /** completed */
    Route::get('form/public/org-link/{uuid}', [App\Http\Controllers\Api\V1\PublicFormLinkController::class, 'show']); /** completed */
    Route::get('form/public/delete-form-link/{link_id}',
        [App\Http\Controllers\Api\V1\PublicFormLinkController::class, 'deleteLink']); /** completed */
    Route::post('form/public/org-links/{form_id}/{org_id}',
        [App\Http\Controllers\Api\V1\PublicFormLinkController::class, 'generateLink']); /** completed */

    Route::get('form/public/shared-form/{slug}',
        [App\Http\Controllers\Api\V1\PublicFormController::class, 'showSharedForm']);
    Route::post('form/public/delete-shared-link/{form_id}',
        [App\Http\Controllers\Api\V1\PublicFormLinkController::class, 'deleteSharedLink']); /** completed */
    Route::post('form/public/org-shared-link',
        [App\Http\Controllers\Api\V1\PublicFormLinkController::class, 'generateSharedLink']); /** completed */

    Route::resource('form/public', 'App\Http\Controllers\Api\V1\PublicFormController');
    Route::get('form/public/ajax-submissions/{id}',
    [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'ajaxSubmissions']);
    Route::get('form/public/{page?}/{limit?}', [App\Http\Controllers\Api\V1\PublicFormController::class, 'index']);
    Route::put('form/public/submissions/{id}/verify/{user_id}/{user_type}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'verify']);
    Route::get('/form/public/accident_report/organisation/{id}/{page?}/{limit?}',
        [App\Http\Controllers\Api\V1\PublicFormController::class, 'accidentReportingFormsForOrganisation']);
    Route::get('/form/public/organisation/{id}/{page?}/{limit?}',
        [App\Http\Controllers\Api\V1\PublicFormController::class, 'formsForOrganisation']);
    Route::get('form/public/org-submissions/{id}/{organisation_id}',
        [App\Http\Controllers\Api\V1\PublicFormController::class, 'submissionsOrg']);
    Route::post('form/public/document/store',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'addDoc']);
    Route::get('/attachment/public/find/{name}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'retrieveAttachment']);
    Route::get('/attachments/public/{submission_id}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'getAllAttachments']);

    //Submission Mapping
    Route::get('forms-mapping-submission/{mapping_id}',
        [App\Http\Controllers\Api\V1\PublicFormSubmissionController::class, 'submissionsMapping']);

    // Forms
    Route::get('form/submissions/{id}', [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'submissions']); /** completed */
    Route::get('form/private/ajax-submissions/{id}',
    [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'ajaxSubmissions']);
    Route::get('form/submissions/{id}/index', [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'index']); /** completed */
    Route::get('form/org-submissions/{id}/{organisation_id}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'submissionsOrg']); /** completed */
    Route::get('/form/{id}', [App\Http\Controllers\Api\V1\FormController::class, 'show']);
    Route::get('form/{page?}/{limit?}', [App\Http\Controllers\Api\V1\FormController::class, 'index']);
    Route::get('formsfororganisation/{organisation_id}/{page?}/{limit?}',
        [App\Http\Controllers\Api\V1\FormController::class, 'formsfororganisation']);
    Route::get('getform/{form_id}', [App\Http\Controllers\Api\V1\FormController::class, 'show']);
    Route::resource('form', 'App\Http\Controllers\Api\V1\FormController');
    Route::resource('form-submission', 'App\Http\Controllers\Api\V1\FormSubmissionController'); /** completed */
    Route::get('/survey-submission/all',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'all']);
    Route::get('/survey-submission/rr-all',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'rrAll']);
    Route::get('/survey-submission/rr-all-tracker',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'rrAllForTracker']);
    Route::get('/survey-submission',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'index']);
    Route::get('/survey-submission/rrgradings-all',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'rrGradingsAll']);
    Route::get('/survey-submission/form/{id}',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'submissions']);
    Route::get('/survey-submission/survey/{id}',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'submissionBySurveyId']);

    // legacy
    Route::get('legacy-survey-submission/{id}',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'showLegacy']);

    Route::resource('survey-submission', 'App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController');
    Route::post('survey-submission/{id}',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'update']);
    Route::post('survey-submission/close/{id}',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'close_issue']);
    Route::get('survey/info/{surveyor_id}/{form_id}/{survey_id?}',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'getFormID']);
    Route::get('/attachment/find/{name}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'retrieveAttachment']); /** completed */

    Route::get('form/saved_forms/{org_id}/{user_id}/{manager}/{level1?}/{level3?}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'savedFormsForOrg']); /** completed */
    Route::get('form/submitted_forms/{org_id}/{user_id}/{manager}/{level1?}/{level3?}',
        [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'submittedFormsForOrg']); /** completed */
    Route::post('form/document/store', [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'addDoc']); /** completed */
    Route::post('form/document/destroy', [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'deleteDoc']); /** completed */
    Route::post('re-action/update',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'updateREAction']);
    Route::post('ChangeRRdates',
        [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'ChangeRRdates']);

    //    Route::resource('form/submissions/{id}', [App\Http\Controllers\Api\V1\FormSubmissionController::class, 'submissions']);

    Route::group(['prefix' => 'risk-improvement'], function () {
        Route::get('form/list', [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'listForms']);
        Route::get('form/list-all', [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'listAllForms']);
        Route::get('form/risk-recs/{form_id}',
            [App\Http\Controllers\Api\V1\RiskImprovementFormSubmissionController::class, 'indexedRiskRecs']);
        Route::get('form/{id}', [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'show']);
        Route::get('form/{page?}/{limit?}',
            [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'index']);
        Route::resource('form', 'App\Http\Controllers\Api\V1\RiskImprovementFormController');
        Route::get('risk-rec/titles/{id?}',
            [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'risk_rec_titles']);
        Route::post('risk-rec/titles/store',
            [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'riskRecTitlesStore']);
        Route::post('risk-rec/titles/update/{id}',
            [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'risk_rec_titles_update']);
        Route::get('risk-rec/titles/delete/{id}',
            [App\Http\Controllers\Api\V1\RiskImprovementFormController::class, 'risk_rec_titles_delete']);

    });

    // Standard Risk Grading
    /* completed */
    Route::group(['prefix' => 'standard-risk'], function () {
        Route::group(['prefix' => 'policy-types'], function () {
            Route::get('/', [App\Http\Controllers\Api\V1\RiskGradingPolicyTypeController::class, 'index']);
            Route::get('/{id}', [App\Http\Controllers\Api\V1\RiskGradingPolicyTypeController::class, 'getPolicyType']);
        });

        Route::group(['prefix' => 'attributes'], function () {
            Route::get('/', [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'getAllAttributes']);
            Route::get('/overview/{id}',
                [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'getRiskGradingOverview']);
            Route::get('/{id}', [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'getAttribute']);
            Route::get('/{id}/sub-attributes/{subAttributeId}',
                [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'getSubAttribute']);
        });

        Route::post('calculate',
            [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'calculateRiskGrading']);
        Route::post('recalculate-gradings',
            [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'recalculateGradings']);
        Route::post('export', [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'export']);
        Route::post('/send-error', [App\Http\Controllers\Api\V1\StandardRiskGradingController::class, 'sendErrorFromRiskGrading']);
    });
    /* end completed */
    

    //Links
    /** completed */
    Route::group(['prefix' => 'link'], function () {
        Route::get('organisation/{id}', [App\Http\Controllers\Api\V1\LinkController::class, 'organisation']);
        Route::get('find/{id}', [App\Http\Controllers\Api\V1\LinkController::class, 'find']);
        Route::get('all', [App\Http\Controllers\Api\V1\LinkController::class, 'all']);
        Route::get('getAvailableLinksForOrgAndSector/{organisation_id}/{sector_id}/{level1}/{level2}/{level3}',
            [App\Http\Controllers\Api\V1\LinkController::class, 'getAvailableLinksForOrgAndSector']);

        Route::get('getAvailableLinksForOrgAndSector/{organisation_id}/{sector_id}/{level1}/{level2}/{level3}',
            [App\Http\Controllers\Api\V1\LinkController::class, 'getAvailableLinksForOrgAndSector']);

        Route::get('organisation/{id}/category/{level1}/{level2?}/{level3?}',
            [App\Http\Controllers\Api\V1\LinkController::class, 'category']);

        Route::get('organisation/{id}/fasDo/{level1}/{sector}',
            [App\Http\Controllers\Api\V1\LinkController::class, 'getFasDoLinks']);

        Route::post('create', [App\Http\Controllers\Api\V1\LinkController::class, 'create']);
        Route::post('update', [App\Http\Controllers\Api\V1\LinkController::class, 'update']);
        Route::post('delete', [App\Http\Controllers\Api\V1\LinkController::class, 'delete']);
    });
    /** end completed */

    //Documents
    Route::group(['prefix' => 'document'], function () {
        //GET
        Route::get('/organisation/{id}',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'findDocumentsPerOrganisation']);
        Route::get('/report/organisation/{id}',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'findReportDocumentsPerOrganisation']);
        Route::get('find/{id}', [App\Http\Controllers\Api\V1\DocumentController::class, 'find']);
        Route::get('find/{id}/organisation/{organisation_id}',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'find_policy_document']);
        Route::get('/organisation/{id}/uploaded',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'findUploadDocumentsPerOrganisation']);
        Route::get('/getdoclib', [App\Http\Controllers\Api\V1\DocumentController::class, 'getdoclib']);
        Route::get('/recommended/{sector_id}', [App\Http\Controllers\Api\V1\DocumentController::class, 'recommended']);
        Route::get('/training-tools/{sector_id?}/{organisation_id?}',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'trainingTools']);
        Route::get('/policy/{id}', [App\Http\Controllers\Api\V1\DocumentController::class, 'get_policy_docs']);

        //POST
        Route::post('store', [App\Http\Controllers\Api\V1\DocumentController::class, 'store']);
        Route::post('destroy/{id}', [App\Http\Controllers\Api\V1\DocumentController::class, 'destroy']);
        Route::post('update', [App\Http\Controllers\Api\V1\DocumentController::class, 'update']);
        Route::post('delete/policy-doc/{document_id}',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'delete_policy_document']);
        Route::post('notify/policy-doc/{id}/{document_id?}',
            [App\Http\Controllers\Api\V1\DocumentController::class, 'notify_admins']);

    });

    //Document policy
    Route::group(['prefix' => 'document-policy'], function () {

        //GET
        Route::get('all', [App\Http\Controllers\Api\V1\DocumentPolicyTypeController::class, 'getDocumentTypes']);
        Route::get('type/{type_id}',
            [App\Http\Controllers\Api\V1\DocumentPolicyTypeController::class, 'get_policy_type']);

        //POST
        Route::post('save/{id?}',
            [App\Http\Controllers\Api\V1\DocumentPolicyTypeController::class, 'save_document_policy_type']);
        Route::post('/type/delete/{id?}',
            [App\Http\Controllers\Api\V1\DocumentPolicyTypeController::class, 'delete_type']);

    });

    // Client Enquiries
    Route::group(['prefix' => 'clientenquiries'], function () {
        Route::get('all', [App\Http\Controllers\Api\V1\ClientEnquiriesController::class, 'getAllClientEnquiries']);
    });

    //Survey cards for kanban
    Route::group(['prefix' => 'survey-cards'], function () {
        Route::get('/',
        [App\Http\Controllers\Api\V1\SurveyCardsController::class, 'index']);
    });

    // Surveys
    Route::group(['prefix' => 'surveys'], function () {
        //GET

        Route::get('legacy-survey-risk-recs/{id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'getLegacySurveyRiskRecs']);

        Route::post('resurvey-check', [App\Http\Controllers\Api\V1\SurveyController::class, 'resurveyCheck']);
        Route::post('store-legacy', [App\Http\Controllers\Api\V1\SurveyController::class, 'storeLegacy']); /** completed */
        Route::post('update-legacy/{id}', [App\Http\Controllers\Api\V1\SurveyController::class, 'updateLegacy']); /** completed */

        Route::get('surveyor/{id}/{submissions?}/{user_type?}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'getSurveysForSurveyors']); /** completed */

        Route::get('organisation/{id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'findDocumentsPerOrganisation']);
        Route::get('broker/{id}/{page?}/{limit?}', [App\Http\Controllers\Api\V1\SurveyController::class, 'broker']); /** completed */
        Route::get('all/{page?}/{limit?}', [App\Http\Controllers\Api\V1\SurveyController::class, 'all']);
        Route::get('legacy-srf', [App\Http\Controllers\Api\V1\SurveyController::class, 'getAllSurveysLegacySrf']);
        Route::get('all-srfs', [App\Http\Controllers\Api\V1\SurveyController::class, 'allSrfs']); /** completed */
        Route::get('options/{column_name?}', [App\Http\Controllers\Api\V1\SurveyController::class, 'options']); /** completed */
        Route::post('legacy-add-attachment-info',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'legacy_add_attachment']); /** completed */
        Route::post('add-attachment-info', [App\Http\Controllers\Api\V1\SurveyController::class, 'add_attachment']); /** completed */

        Route::post('add-survey-attachments-empty',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'add_survey_attachment_empty']); /** completed */

        Route::post('add-resurvey-attachments',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'addResurveyAttachments']);

        Route::post('update-survey-attachments-empty',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'update_survey_attachment_empty']); /** completed */

        Route::post('survey-agenda', [App\Http\Controllers\Api\V1\SurveyController::class, 'survey_agenda']); /** completed */

        Route::get('get-survey-files-for-id/{id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'get_survey_attatchment_empty']); /** completed */
        Route::get('get-srf-for-id/{id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'get_survey_srf_empty']); /** completed */
        Route::post('destroy-survey-attachment-empty',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'destroy_survey_attatchment_empty']); /** completed */
        Route::post('destroy-legacy-survey-attachment-empty',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'destroy_legacy_survey_attatchment_empty']); /** completed */

        Route::get('legacy-get-attachment-info/{field_name}/{survey_id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'legacy_get_attachment']); /** completed */
        Route::get('get-attachment-info/{field_name}/{survey_id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'get_attachment']); /** completed */

        Route::get('get-riskrec-attachments/{survey_id}',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'getAttachmentForRiskRec']); /** completed */

        Route::post('add-attachment-caption',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'add_attachment_caption']); /** completed */
        Route::post('add-attachment-annotation',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'add_attachment_annotation']); /** completed */
        Route::post('add-attachment-geolocation',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'add_attachment_geolocation']); /** completed */

        Route::post('update_actual/{id}', [App\Http\Controllers\Api\V1\SurveyController::class, 'updateSurvey']); /** completed */
        Route::post('update-survey-status',
            [App\Http\Controllers\Api\V1\SurveyController::class, 'updateSurveyStatus']); /** completed */

        Route::get('{survey_id}/delete/{attachment_id}', [App\Http\Controllers\Api\V1\SurveyController::class, 'delete_attachment']); /** completed */
        Route::get('delete/{survey_id}', [App\Http\Controllers\Api\V1\SurveyController::class, 'delete']); /** completed */

        Route::get('{id}/legacy', [App\Http\Controllers\Api\V1\SurveyController::class, 'getLegacy']); /** completed */
        Route::get('checkClientOrgSurvey/{organisation_id}', [App\Http\Controllers\Api\V1\SurveyController::class, 'checkClientOrgHasSurvey']);

        // Re productivity metrics
        Route::get('{survey_id}/risk-recommendations', [App\Http\Controllers\Api\V1\SurveyController::class, 'getRiskRecommendations']);
    });

    Route::resource('surveys', 'App\Http\Controllers\Api\V1\SurveyController',
        ['only' => ['store', 'update', 'show', 'destroy']]);

    // CSR - microsite
    Route::group(['prefix' => 'microsite'], function () {
        Route::get('report-exist/{survey_id}/{email}', [App\Http\Controllers\Api\V1\MicrositeController::class, 'reportExist']);
        Route::get('risk-grading/{survey_id}/{is_ordered_by_sub_attr_id?}', [App\Http\Controllers\Api\V1\MicrositeController::class, 'riskGradingSettings']);
        Route::post('risk-grading-re-order', [App\Http\Controllers\Api\V1\MicrositeController::class, 'riskGradingReOrder']);
        Route::post('toggle-column', [App\Http\Controllers\Api\V1\MicrositeController::class, 'toggleColumn']);
        Route::post('toggle-commentary', [App\Http\Controllers\Api\V1\MicrositeController::class, 'toggleCommentary']);
        Route::get('generate-otp', [App\Http\Controllers\Api\V1\MicrositeController::class, 'generateOtp']);
        Route::post('send-to-client', [App\Http\Controllers\Api\V1\MicrositeController::class, 'sendToClient']);
        Route::post('send-otp-to-client', [App\Http\Controllers\Api\V1\MicrositeController::class, 'sendOtpToClient']);
        Route::post('auth', [App\Http\Controllers\Api\V1\MicrositeController::class, 'auth']);
        Route::get('risk-grading-tooltip', [App\Http\Controllers\Api\V1\MicrositeController::class, 'getRiskGradingTooltip']);
        Route::get('risk-grading-narratives', [App\Http\Controllers\Api\V1\MicrositeController::class, 'getRiskGradingNarratives']);
    });

    // Schedule
    /** completed */
    Route::group(['prefix' => 'schedule'], function () {
        //GET
        Route::get('{start}/{end}', [App\Http\Controllers\Api\V1\ScheduleController::class, 'range'])
            ->where('start', '^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$')
            ->where('end', '^[0-9]{4}-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$');
        Route::post('add-schedule-attachments-empty',
            [App\Http\Controllers\Api\V1\ScheduleController::class, 'add_schedule_attachment_empty']);
        Route::post('update-schedule-attachments-empty',
            [App\Http\Controllers\Api\V1\ScheduleController::class, 'update_schedule_attachment_empty']);
        Route::get('/category-types-for-schedule/{id}',
            [App\Http\Controllers\Api\V1\ScheduleController::class, 'getCategoryTypesByOrganisation']);
        Route::post('delete', [App\Http\Controllers\Api\V1\ScheduleController::class, 'destroy']);
        Route::get('/schedule-for-organisation/{id}/{lastlogin}',
            [App\Http\Controllers\Api\V1\ScheduleController::class, 'getSchedulesForOrganisation']);
        Route::get('/schedule-for-client/{id}',
            [App\Http\Controllers\Api\V1\ScheduleController::class, 'getSchedulesForClient']);
    });
    /** end completed */

    Route::resource('schedule', 'App\Http\Controllers\Api\V1\ScheduleController',
        ['only' => ['store', 'update', 'show', 'destroy']]);

    //Loss Lessons
    Route::group(['prefix' => 'loss-lesson'], function () {
        Route::get('/organisation/{id}', [App\Http\Controllers\Api\V1\LossLessonController::class, 'index']);
        Route::get('/organisation/{id}/{lossLesson}',
            [App\Http\Controllers\Api\V1\LossLessonController::class, 'show']);
        Route::delete('/organisation/{id}/{lossLesson}',
            [App\Http\Controllers\Api\V1\LossLessonController::class, 'destroy']);
        Route::post('/organisation/{id}/{lossLesson}',
            [App\Http\Controllers\Api\V1\LossLessonController::class, 'update']);
    });

    Route::get('/loss-lesson/all/{sector?}',
        [\App\Http\Controllers\Api\V1\LossLessonController::class, 'all'])->name('loss-lesson.all');
    Route::get('/loss-lesson/featured/{sector?}',
        [\App\Http\Controllers\Api\V1\LossLessonController::class, 'featured'])->name('loss-lesson.featured');

    //Loss Lessons
    Route::group(['prefix' => 'messaging'], function () {
        Route::post('/send', [App\Http\Controllers\Api\V1\MessagingController::class, 'send']); /** completed */
        Route::get('/thread-count/{survey_id}', [App\Http\Controllers\Api\V1\MessagingController::class, 'threadCount']);
        Route::get('/notify', [App\Http\Controllers\Api\V1\MessagingController::class, 'sendNotifications']); /** completed */
        Route::get('/survey/{id}', [App\Http\Controllers\Api\V1\MessagingController::class, 'threadForSurvey']); /** completed */
        Route::get('/survey/{id}/{risk_rec}',
            [App\Http\Controllers\Api\V1\MessagingController::class, 'threadForRiskRec']); /** completed */

    });

    //Risk Recommendations
    Route::group(['prefix' => 'risk-recommendations'], function () {
        Route::get('{survey_id}/report/export', [App\Http\Controllers\Api\V1\RiskRecommendationController::class, 'exportSurveyReport']);
        // TODO: find which route it is pointing to
//        Route::get('/all', array('as' => 'risk-recommendations.all', 'uses' => 'Risk@all'));
        Route::get('/cards', [RiskRecommendationCardsController::class, 'index']);
        Route::post('/export', [RiskRecommendationCardsController::class, 'export']);
        Route::post('/queueExport', [RiskRecommendationCardsController::class, 'queueExport']);
        Route::post('/processExport', [RiskRecommendationCardsController::class, 'processExport']);
        Route::get('/{survey_id}/cards', [RiskRecommendationCardsController::class, 'getCardsBySurvey']);
    });

    //Asset Library
    Route::Group(['prefix' => 'assets'], function () {
        Route::get('/attributes/{entities}',
            [App\Http\Controllers\Api\V1\AssetController::class, 'attributes'])->name('assets.attributes');
        Route::get('/values/{entity}',
            [App\Http\Controllers\Api\V1\AssetController::class, 'values'])->name('assets.values');
        Route::post('create', [App\Http\Controllers\Api\V1\AssetController::class, 'store'])->name('assets.create');
    });

    // Sector
    Route::get('sector/options', [App\Http\Controllers\Api\V1\LearningCourseController::class, 'getAllSectors']); /** completed */

    // Learning System
    Route::group(['prefix' => 'learning'], function () {

        // Assign Sector Courses
        Route::get('course/{id}/sectors',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignedSectors']); /** completed */
        Route::post('course/{id}/sectors',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignedNewSectors']); /** completed */

        // Assign Courses
        Route::post('course/{id}/assign',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignCourse']); /** completed */
        Route::get('course/{id}/assign', [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignCourse']); /** completed */
        Route::post('course/{id}/organisations',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignOrganisation']); /** completed */
        Route::get('course/{id}/organisations',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignOrganisation']); /** completed */ 
            
        Route::post('course/{course_id}/assign',
        [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignCourse']); /** completed */

        Route::get('course/user/{user_id}/{type}',
        [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignedToUser']); /** completed */

        // Assignments
        Route::get('/assignment/{user_id}', [App\Http\Controllers\Api\V1\LearningAssignmentController::class, 'show']);

        // Categories
        Route::get('course/categories/all/{page?}/{limit?}',
            [App\Http\Controllers\Api\V1\LearningCategoryController::class, 'all']);
        Route::get('course/categories/options',
            [App\Http\Controllers\Api\V1\LearningCategoryController::class, 'options']);
        Route::resource('course/categories', 'App\Http\Controllers\Api\V1\LearningCategoryController',
            ['only' => ['store', 'update', 'show', 'destroy']]);

        // Courses

        Route::get('course/checkcourses/{user_id}',
        [App\Http\Controllers\Api\V1\LearningCourseController::class, 'getUserCourses']); /** completed */

        Route::resource('course', 'App\Http\Controllers\Api\V1\LearningCourseController',
            ['only' => ['store', 'update', 'show', 'destroy']]); /** completed */

        Route::get('course/{course_id}/view', [App\Http\Controllers\Api\V1\LearningCourseController::class, 'view']); /** completed */
        Route::get('course/{course_type}/{page?}/{limit?}/{organisationId?}/{userId?}/{isResponsibleBusiness?}',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'all']); /** completed */

        Route::get('/courseProgress', [App\Http\Controllers\Api\V1\LearningCourseController::class, 'getProgress']); /** completed */
        Route::get('/courseReports', [App\Http\Controllers\Api\V1\LearningCourseController::class, 'getReports']); /** completed */
        Route::get('course/options', [App\Http\Controllers\Api\V1\LearningCourseController::class, 'options']); /** completed */
        Route::get('course/{course_id}/lessons/{page?}/{limit?}',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'byCourseID']);
        Route::post('course/{course_id}/cert',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'createCertificate']); /** completed */
        Route::post('course/{course_id}/reminder',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'reminder']); /** completed */
        Route::post('course/{course_id}/duplicate',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'duplicateCourse']); /** completed */



        // Assign Courses


        Route::post('course/{id}/organisations',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignOrganisation']); /** completed */
        Route::get('course/{id}/organisations',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignOrganisation']); /** completed */

        // Assign Sector Courses
        Route::get('course/{id}/sectors',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignedSectors']); /** completed */
        Route::post('course/{id}/sectors',
            [App\Http\Controllers\Api\V1\LearningCourseController::class, 'assignedNewSectors']); /** completed */


        // Course Image
        Route::delete('course-image/{type}/{path}',
            [App\Http\Controllers\Api\V1\LearningCourseImageController::class, 'destroy']);

        // Lessons
        Route::get('lesson/{lesson_id}/view', [App\Http\Controllers\Api\V1\LearningLessonController::class, 'view']); /** completed */
        Route::get('lesson/{lesson_id}/duplicate',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'duplicate']); /** completed */
        Route::post('lesson/{lesson_id}/duplicate',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'duplicate']); /** completed */
        Route::get('/lesson/all/{page?}/{limit?}',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'all']); /** completed */
        Route::get('/lesson/user/{user_id?}',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'userLessons']); /** completed */
        Route::post('/lesson/{lesson_id}/open',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'openLessons']); /** completed */
        Route::put('/course/{course_id}/lesson/sort',
            [App\Http\Controllers\Api\V1\LearningLessonController::class, 'sort']); /** completed */
        Route::resource('lesson', 'App\Http\Controllers\Api\V1\LearningLessonController',
            ['only' => ['store', 'update', 'show', 'destroy',]]); /** completed */

        //Pages

        Route::Group(['prefix' => 'lesson/{lesson_id}/page'], function () {

            Route::get('{page_id}', [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'show']); /** completed */

            Route::post('{page_id}/section',
                [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'storeSection']); /** completed */
            Route::post('/', [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'store']); /** completed */
            Route::post('{page_id}/duplicate',
                [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'duplicate']); /** completed */
            Route::post('{page_id}/section/{section_id}/duplicate',
                [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'duplicateSection']); /** completed */

            Route::delete('{page_id}', [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'delete']); /** completed */
            Route::delete('{page_id}/section/{section_id}',
                [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'deleteSection']); /** completed */

            Route::put('{page_id}/section/{section}',
                [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'updateSection']); /** completed */
            Route::put('/sort', [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'sortPages']); /** completed */
            Route::put('{page_id}/sort',
                [App\Http\Controllers\Api\V1\LearningLessonPageController::class, 'sortSections']); /** completed */

        });
        //tests
        Route::Group(['prefix' => 'lesson/{lesson_id}/test'], function () {

            Route::get('', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'show']); /** completed */
            Route::post('/check', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'check']); /** completed */
            Route::post('/finish', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'finishTest']); /** completed */
            Route::post('/result', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'getAttempt']); /** completed */
            Route::post('option', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'setOptions']); /** completed */
            Route::post('section', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'storeSection']); /** completed */
            Route::put('update', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'update']); /** completed */
            Route::post('', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'store']); /** completed */
            Route::post('duplicate', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'duplicate']); /** completed */
            Route::put('section/sort',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'sortSection']); /** completed */
            Route::post('section/{section_id}/duplicate',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'duplicateSection']); /** completed */
            Route::post('section/{section_id}/option/{question_type}',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'addOption']); /** completed */
            Route::post('section/{section_id}/changequestiontype/{question_type}',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'changeQuestionType']); /** completed */
            Route::delete('section/{section_id}/option/{option_id}',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'removeOption']); /** completed */
            Route::delete('section/{section_id}',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'deleteSection']); /** completed */
            Route::delete('', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'delete']); /** completed */

            Route::put('section/{section}',
                [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'updateSection']); /** completed */

            Route::put('sort', [App\Http\Controllers\Api\V1\LearningLessonTestController::class, 'sortSections']); /** completed */

        });

    });

    Route::post('aspen/reply', [App\Http\Controllers\Api\V1\AspenController::class, 'reply']);
    Route::get('aspen/organisation/{id}', [App\Http\Controllers\Api\V1\AspenController::class, 'organisation']);
    Route::get('aspen/all/{page?}/{limit?}/{priority}/{direction?}/{aspen_user?}/{libery_user?}/{client?}/{status?}/{order?}',
        [App\Http\Controllers\Api\V1\AspenController::class, 'all']);
    Route::get('aspen-documents-access/all/{page?}/{limit?}/{search?}',
        [App\Http\Controllers\Api\V1\AspenDocumentsController::class, 'documentsAccess']);
    Route::resource('aspen', 'App\Http\Controllers\Api\V1\AspenController');
    Route::get('aspen-documents/organisation/{id}',
        [App\Http\Controllers\Api\V1\AspenDocumentsController::class, 'organisation']);
    Route::get('aspen-documents/code/{id}', [App\Http\Controllers\Api\V1\AspenDocumentsController::class, 'code']);
    Route::resource('aspen-documents', 'App\Http\Controllers\Api\V1\AspenDocumentsController');
    Route::get('daily-claims-run/all', [App\Http\Controllers\Api\V1\DcrDocumentController::class, 'all']);
    Route::post('daily-claims-run/upload', [App\Http\Controllers\Api\V1\DcrDocumentController::class, 'upload']);
    Route::group(['prefix' => 'endorsement-wordings'], function () {
        Route::get('all', [App\Http\Controllers\Api\V1\EwDocumentController::class, 'all']);
        Route::get('find/{id}', [App\Http\Controllers\Api\V1\EwDocumentController::class, 'find']);
        Route::post('update', [App\Http\Controllers\Api\V1\EwDocumentController::class, 'update']);
        Route::post('store', [App\Http\Controllers\Api\V1\EwDocumentController::class, 'store']);
        Route::post('destroy/{id}', [App\Http\Controllers\Api\V1\EwDocumentController::class, 'destroy']);
        Route::get('authorize/{id}', [App\Http\Controllers\Api\V1\EwDocumentController::class, 'authorise']);
    });

    Route::post('weblog', [LibertyUserController::class, 'webLog']);

    Route::post('video-call', [App\Http\Controllers\Api\V1\VideoCallController::class, 'generateRoom']);
    Route::post('video-call/login', [App\Http\Controllers\Api\V1\VideoCallController::class, 'login']);
    Route::get('video-call/token/{identity}/{roomName}',
        [App\Http\Controllers\Api\V1\VideoTokenController::class, 'token']);
    Route::post('video-call/statuscallback', [App\Http\Controllers\Api\V1\VideoCallLogController::class, 'log']);
    Route::post('video-call/display', [App\Http\Controllers\Api\V1\VideoCallLogController::class, 'downloadLog']);

    Route::group(['prefix' => 'responsible-business'], function () {
        Route::get('get-articles',
            [App\Http\Controllers\Api\V1\ResponsibleBusiness\ArticleController::class, 'getArticles']);
        Route::get('get-article/{id}',
            [App\Http\Controllers\Api\V1\ResponsibleBusiness\ArticleController::class, 'getArticle']);
        Route::get('get-announcements',
            [App\Http\Controllers\Api\V1\ResponsibleBusiness\AnnouncementController::class, 'getAnnouncements']);
        Route::get('get-announcement/{id}',
            [App\Http\Controllers\Api\V1\ResponsibleBusiness\AnnouncementController::class, 'getAnnouncement']);
    });

    Route::group(['prefix' => 'custom-sectors'], function () {
        Route::get('get-articles', [App\Http\Controllers\Api\V1\customSectors\ArticleController::class, 'getArticles']);
        Route::get('get-article/{id}',[App\Http\Controllers\Api\V1\customSectors\ArticleController::class, 'getArticle']);
        Route::get('get-announcements',[App\Http\Controllers\Api\V1\customSectors\AnnouncementController::class, 'getAnnouncements']);
        Route::get('get-announcement/{id}', [App\Http\Controllers\Api\V1\customSectors\AnnouncementController::class , 'getAnnouncement']);
    });

    Route::prefix('virtual-rooms')->group(function () {
        Route::get('staff/{email}', [VideoCallController::class, 'staff']);
    });

// @NOTE: Decommissioned
//     Route::group(['prefix' => 'virtual-rooms'], function () {
//         Route::post('video-call-outlook',
//             [VideoCallController::class, 'generateRoomFromOutlook']);
//         Route::post('video-call', [VideoCallController::class, 'generateRoom']);
//         Route::get('schedules/{date?}', [VideoCallController::class, 'schedules']);
//         Route::post('schedules', [VideoCallController::class, 'schedules']);
//         Route::post('schedules-client',
//             [VideoCallController::class, 'schedulesForClient']);
//         Route::post('get-schedules/{date?}',
//             [VideoCallController::class, 'schedules']);
//         Route::get('rep-schedules/{person_id}/{date?}',
//             [VideoCallController::class, 'getRepSchedules']);
//         Route::get('landing-page-content',
//             [VideoCallController::class, 'landingPageContent']);
//         Route::get('get-bookings', [VideoCallController::class, 'getBookings']);
//         Route::post('get-booking-details',
//             [VideoCallController::class, 'getBookingDetails']);
//         Route::post('create-availability',
//             [VideoCallController::class, 'createRepresentativeCallAvaialbility']);
//         Route::post('video-call/login', [VideoCallController::class, 'login']);
//         Route::get('video-call/token/{identity}/{roomName}',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoTokenController::class, 'token']);
//         Route::post('video-call/statuscallback',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoCallLogController::class, 'log']);
//         Route::post('video-call/display',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoCallLogController::class, 'downloadLog']);
//         Route::post('delete-booking',
//             [VideoCallController::class, 'deleteBooking']);
//         Route::post('add-participant',
//             [VideoCallController::class, 'addParticipant']);
//         Route::post('add-multiple-participant',
//             [VideoCallController::class, 'addMultipleParticipant']);
//         Route::get('{uuid}/getattachedfiles',
//             [VideoCallController::class, 'getAttachedFiles']);
//         Route::get('check-call-now-bookings',
//             [VideoCallController::class, 'checkCallNowBookings']);
//         Route::post('twilio-lookup', [VideoCallController::class, 'twilioLookUp']);
//         Route::get('terms-and-conditions',
//             [VideoCallController::class, 'termsAndConditions']);
//         Route::resource('external-contacts', 'App\Http\Controllers\Api\V1\LetsTalk\ExternalContactsController');
//         Route::get('get-lib-rep', [VideoCallController::class, 'getLibRepByName']);
//         Route::get('video-call/schedule/{identity}/{roomName}',
//             [VideoCallController::class, 'getParticipantSchedule']);
//         Route::get('export-contacts',
//             [VideoCallController::class, 'exportContacts']);
//         Route::get('export-profiles',
//             [VideoCallController::class, 'exportProfiles']);
//         Route::post('remove-all-repeated-availability-slots',
//             [VideoCallController::class, 'removeAllRecurringAvailability']);
//         Route::post('import-contacts',
//             [VideoCallController::class, 'importContacts']);
//         Route::get('video-record/{token}/token',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoRecordController::class, 'token']);
//         Route::get('staff/{email}', [VideoCallController::class, 'staff']);

//         Route::post('video-record/{token}/video-upload',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoRecordController::class, 'videoUpload']);
//         Route::post('video-record/my-messages',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoRecordController::class, 'getMyMessages']);
//         Route::post('video-record/my-messages-members',
//             [App\Http\Controllers\Api\V1\LetsTalk\MyMessageController::class, 'getMyMessageMembers']);
//         Route::post('video-record/update-notification',
//             [App\Http\Controllers\Api\V1\LetsTalk\MyMessageController::class, 'updateNotification']);
//         Route::post('video-record/red-dot-notifications',
//             [App\Http\Controllers\Api\V1\LetsTalk\MyMessageController::class, 'getRedDotNotifications']);
//         Route::get('video-record/single-page-view/{id}',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoRecordController::class, 'singlePageView']);
//         Route::post('video-record/customer-video-message/',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoRecordController::class, 'customerVideoMessage']);
//         Route::get('video-record/{token}/{cms_id}/get-uploaded-video',
//             [App\Http\Controllers\Api\V1\LetsTalk\VideoRecordController::class, 'getUploadedVideo']);

//         Route::post('video-record/customer-my-messages',
//             [App\Http\Controllers\Api\V1\LetsTalk\MyMessageController::class, 'getCustomerMyMessages']);

//         Route::post('generate-link',
//             [App\Http\Controllers\Api\V1\LetsTalk\AuthenticateUserController::class, 'generateLink']);
//         Route::post('customer-generate-link',
//             [App\Http\Controllers\Api\V1\LetsTalk\AuthenticateUserController::class, 'customerGenerateLink']);
//         Route::post('authenticate',
//             [App\Http\Controllers\Api\V1\LetsTalk\AuthenticateUserController::class, 'authenticate']);
//         Route::get('get-available-representative/{page?}/{search?}/{person_id?}',
//             [VideoCallController::class, 'getAvailableRepresentative']);
//         Route::post('chat-with-me', [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'chatWithMe']);
//         Route::post('delete-schedule',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'deleteSchedule']);
//         Route::post('check-dot-notifications',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomDotNotificationController::class, 'getDotNotification']);
//         Route::post('dismiss-theme-room-dot-notifications',
//             [
//                 App\Http\Controllers\Api\V1\LetsTalk\SocialRoomDotNotificationController::class,
//                 'dismissThemeRoomNotification',
//             ]);
//         Route::post('dismiss-theme-room-dot-notification-message',
//             [
//                 App\Http\Controllers\Api\V1\LetsTalk\SocialRoomDotNotificationController::class,
//                 'dismissThemeRoomNotificationMessage',
//             ]);
//         Route::post('update-visited-dot-notification',
//             [
//                 App\Http\Controllers\Api\V1\LetsTalk\SocialRoomDotNotificationController::class,
//                 'updateVisitedRedDotNotification',
//             ]);

//         Route::post('room-check', [App\Http\Controllers\Api\V1\LetsTalk\WaitingRoomController::class, 'roomCheck']);
//         Route::post('room-finder', [App\Http\Controllers\Api\V1\LetsTalk\WaitingRoomController::class, 'roomFinder']);
//         Route::post('identity-check',
//             [App\Http\Controllers\Api\V1\LetsTalk\WaitingRoomController::class, 'identityCheck']);
//         Route::post('business-check',
//             [App\Http\Controllers\Api\V1\LetsTalk\WaitingRoomController::class, 'businessCheck']);

//         Route::get('room/{name}/participants/all',
//             [App\Http\Controllers\Api\V1\LetsTalk\ParticipantsController::class, 'getOrderedParticipantsByRoom']);
//         Route::get('room/{roomCode}/participants/{userCode}/broker',
//             [App\Http\Controllers\Api\V1\LetsTalk\ParticipantsController::class, 'getParticipantBrokerDetails']);

//         Route::get('get-login-attempts',
//             [App\Http\Controllers\Api\V1\LetsTalk\AuthenticateUserController::class, 'getLoginAttemptLogs']);
//         Route::post('delete-login-attempt',
//             [App\Http\Controllers\Api\V1\LetsTalk\AuthenticateUserController::class, 'deleteLoginAttemptLog']);

//         //social rooms routes
//         Route::get('social-space-rooms',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'getSocialSpaceRooms']);
//         Route::get('/{status}/{identity}/{roomName}/{room_type}/status',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'updateParticipantStatus']);
//         Route::post('social-rooms',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'getSocialRooms']);
//         Route::get('social-rooms/{identity}/{roomName}/last-activity-check',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'updateParticipantLastActivity']);
//         Route::get('social-rooms/{userCode}/{roomCode}/participants',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'getParticipants']);
//         Route::get('social-rooms/{room_type_id}/archive',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'archiveRooms']);
//         Route::get('social-rooms/{roomCode}',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'getSocialRoomDetails']);
//         Route::get('{room_name}/social-room-type',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'getSocialRoomType']);
//         Route::group(['prefix' => 'catch-up'], function () {
//            Route::post('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'store']);
//            Route::get('/upcoming',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'getUpcomingCatchups']);
//            Route::get('/colleagues',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'getColleagues']);
//            Route::get('/representatives',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'getRepresentatives']);
//            Route::get('/{room_id}', [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'show']);

//            Route::post('/delete-schedule',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'deleteSchedule']);
//            Route::delete('/{room_id}',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialCatchUpController::class, 'deleteSpace']);
//         });

//         Route::group(['prefix' => 'cyber-vr'], function () {
//             Route::post('generateLink', [CyberGuestController::class, 'generateLink']);
//         });

//         // Social Room Polls
//         Route::post('social-rooms/{roomCode}/polls',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomPollsController::class, 'store']);
//         Route::put('social-rooms/{roomCode}/polls/{pollId}',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomPollsController::class, 'update']);
//         Route::delete('social-rooms/{roomCode}/polls/{pollId}',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomPollsController::class, 'delete']);
//         Route::post('social-rooms/{roomCode}/polls/{pollId}/votes',
//             [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomPollVotesController::class, 'store']);

//        Route::group(['prefix' => 'team-meeting'], function () {
//            Route::post('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'store']);
//            Route::get('/upcoming',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'getUpcomingTeamMeetings']);
//            Route::get('/teams', [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'getTeams']);
//            Route::get('/representatives',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'getRepresentatives']);
//            Route::post('/{room_id}',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'show']);
//            Route::get('/{room_id}/social-room',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'getSocialRoomWithId']);
//            Route::post('/{room_id}/edit-social-room',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'editTeamMeeting']);
//            Route::post('/{room_id}/edit-room-title',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialTeamMeetingController::class, 'editRoomTitle']);
//        });

//        Route::group(['prefix' => 'slt-rooms'], function () {
//            Route::get('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'index']);
//            Route::post('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'store']);
//            Route::post('/delete-schedule',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'deleteSchedule']);
//            Route::post('/{room_id}', [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'show']);
//            Route::delete('/{room_id}',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'deleteSpace']);
//            Route::post('/{room_id}/create-schedule',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'createSchedule']);
//            Route::get('/slt-moderators',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'getCmsSltModerators']);
//            Route::get('/space-by-creator',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialSltRoomsController::class, 'getAllSltSpaceByCreator']);
//        });

//        Route::post('/{room_type_id}/{room_id}/download-ics',
//            [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomScheduleController::class, 'downloadIcs']);

//        Route::group(['prefix' => 'social-room-schedule'], function () {
//            Route::post('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomScheduleController::class, 'store']);
//        });

//        Route::post('join-room', [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'joinRoom']);
//        Route::post('room-creation',
//            [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'roomCreation']);
//        Route::get('{approved_by}/{room_id}/{approval_type}/room-approval',
//            [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomController::class, 'roomApproval']);
//        Route::group(['prefix' => 'theme-rooms'], function () {
//            Route::get('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'index']);
//            Route::post('/', [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'store']);
//            Route::get('/communities',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'communities']);
//            Route::get('/sync-communities',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'syncCommunities']);
//            Route::post('/pinned', [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'pinned']);
//            Route::post('/togglepin',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'togglepin']);
//            Route::get('/{room_id}', [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'show']);
//            Route::get('/updated-schedule/{room_code}',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'updatedSchedule']);
//            Route::post('/delete-schedule',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'deleteSchedule']);
//            Route::delete('/{room_id}',
//                [App\Http\Controllers\Api\V1\LetsTalk\SocialThemeRoomsController::class, 'deleteSpace']);

//        });
// //
//         Route::group(['prefix' => 'discussion/'], function () {


//             Route::group(['prefix' => 'file/'], function () {
//                 Route::post('upload',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageFileController::class, 'upload']);
//                 Route::post('download',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageFileController::class, 'download']);
//                 Route::delete('{id}/delete',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageFileController::class, 'delete']);
//                 Route::get('{id}',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageFileController::class, 'link']);
//             });


//             Route::group(['prefix' => 'reply/'], function () {
//                 Route::get('', [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageController::class, 'index']);
//                 Route::post('store',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageController::class, 'store']);
//                 Route::post('{id}/update',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageController::class, 'update']);
//                 Route::delete('{id}/delete',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageController::class, 'delete']);
//                 Route::put('{id}/like',
//                     [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageController::class, 'like']);
//                 Route::get('{id}', [App\Http\Controllers\Api\V1\LetsTalk\SocialRoomMessageController::class, 'show']);

//             });
//         });


//        Route::group(['prefix' => 'announcements'], function () {
//            Route::post('/', [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'getAll']);
//            Route::post('/all',
//                [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'getAllAnnouncements']);
//            Route::post('/editor', [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'isEditor']);
//            Route::post('/promoted', [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'promoted']);
//            Route::get('/categories',
//                [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'categories']);
//            Route::get('/categories/{category_id}',
//                [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'category']);
//            Route::get('/{announcement_id}',
//                [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'show']);
//            Route::get('/{announcement_id}/related',
//                [App\Http\Controllers\Api\V1\LetsTalk\AnnouncementController::class, 'related']);
//        });
// //
// //        Route::post('/customer-spaces',
// //            [App\Http\Controllers\Api\V1\LetsTalk\SocialCustomerRoomController::class, 'getAll']);
// //
// //        Route::post('/import-liberty-users',
// //            [App\Http\Controllers\Api\V1\LetsTalk\ImportExportController::class, 'importLibertyUsers']);
// //
//     Route::get('promotions/videos/latest',
//         [App\Http\Controllers\Api\V1\LetsTalk\PromotionsController::class, 'index']);
//     });
    Route::post('unsubscribe-sms', [App\Http\Controllers\Api\V1\SubscriptionController::class, 'unsubscribeSms']);
    Route::get('unsubscribe-email/{token}',
        [App\Http\Controllers\Api\V1\SubscriptionController::class, 'unsubscribeEmail']);
    Route::get('suppressions/{type}', [App\Http\Controllers\Api\V1\SubscriptionController::class, 'suppressions']);
    Route::get('suppressions/email/delete/{token}',
        [App\Http\Controllers\Api\V1\SubscriptionController::class, 'deleteEmailSuppression']);

    Route::group(['prefix' => 'cqlive'], function () {
        Route::post('add-payload', [App\Http\Controllers\Api\V1\CQLive\CQLiveController::class, 'storePayload']);
        Route::get('get-payload', [App\Http\Controllers\Api\V1\CQLive\CQLiveController::class, 'getPayload']);
    });

    Route::group(['prefix' => 'client-claims'], function () {
        Route::post('/', [App\Http\Controllers\Api\V1\ClientClaims\ClientClaimsController::class, 'all']);
        Route::post('/filter-data',
            [App\Http\Controllers\Api\V1\ClientClaims\ClientClaimsController::class, 'getFilterData']);
        Route::get('/component-order', [App\Http\Controllers\Api\V1\ClientClaims\ClientClaimsController::class, 'getComponentOrder']);
    });

    // GDPR and Data Privacy routes
    Route::group(['prefix' => 'gdpr', 'as' => 'gdpr.'], function () {
        Route::group(['prefix' => 'data-privacy', 'as' => 'data-privacy.'], function () {
            Route::post('/search', [App\Http\Controllers\Api\V1\GDPR\DataPrivacyController::class, 'index']);
            Route::get('/for-api/{action}', [App\Http\Controllers\Api\V1\GDPR\DataPrivacyController::class, 'action'])
            ->where('action', 'delete|anonymise')
            ->name('for-api-action');
        });
    });

    Route::group(['prefix' => 'portfolio-views'], function () {
        Route::group(['prefix' => 'insights'], function () {
            Route::get("/", [App\Http\Controllers\Api\V1\PortfolioViews\InsightsController::class, 'index']);
            Route::post("/", [App\Http\Controllers\Api\V1\PortfolioViews\InsightsController::class, 'store']);

            Route::get("/{id}", [App\Http\Controllers\Api\V1\PortfolioViews\InsightsController::class, 'show']);
            Route::post("/{id}", [App\Http\Controllers\Api\V1\PortfolioViews\InsightsController::class, 'update']);
            Route::delete("/{id}", [App\Http\Controllers\Api\V1\PortfolioViews\InsightsController::class, 'delete']);

            Route::get("/templates/{template}", [App\Http\Controllers\Api\V1\PortfolioViews\InsightsController::class, 'showByTemplate']);
        });
    });

    Route::group(['prefix' => 'previous-passwords'], function () {
        Route::post("/check", [App\Http\Controllers\Api\V1\PreviousPasswordController::class, 'check']);
    });

    Route::group(['prefix' => 'previsico'], function () {
        Route::get("/flood-map/{orgid}/{userId}", [App\Http\Controllers\Api\V1\Previsico\FloodMapController::class, 'index']);
    });

    Route::group(['prefix' => 're-metrics'], function () {
        Route::get('/organisation/average-risk-grading', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getAverageRiskGrading']);
        Route::get('/organisation/accounts-under-management', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getAccountsUnderManagement']);
        Route::get('/organisation/active-organisations', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getActiveOrganizations']);
        Route::get('/organisation/inactive-organisations', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getInactiveOrganizations']);
        Route::get('/organisation/count-by-sector', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getOrganisationCountBySector']);
        Route::get('/organisation/re-meeting-held', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getRiskEngineeringMeetingHeld']);
        Route::get('/organisation/overview-updated', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getOrganisationOverviewUpdated']);
        Route::get('/organisation/with-survey-programme', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getOrganisationWithSurveyProgramme']);
        Route::get('/organisation/organisation-table-data', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getOrganisationTableData']);
        Route::get('/organisation/risk-recommendation-count-by-status', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getRiskRecommendationCountByStatus']);
        Route::get('/organisation/desktop-reviews-impact', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getBoundData']);
        Route::get('/organisation/srfs', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getSrfs']);
        Route::get('/organisation/dtrs', [App\Http\Controllers\Api\V1\REMetrics\OrganisationController::class, 'getDtrs']);

        Route::get('/organisation/count-by-sector/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getOrganisationCountBySector']);
        Route::get('/organisation/re-meeting-held/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getRiskEngineeringMeetingHeld']);
        Route::get('/organisation/overview-updated/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getOrganisationOverviewUpdated']);
        Route::get('/organisation/with-survey-programme/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getOrganisationWithSurveyProgramme']);
        Route::get('/organisation/risk-recommendation-count-by-status/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getRiskRecommendationCountByStatus']);
        Route::get('/organisation/desktop-reviews-impact/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getBoundData']);
        Route::get('/organisation/srfs/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getSrfs']);
        Route::get('/organisation/dtrs/detailed', [App\Http\Controllers\Api\V1\REMetrics\OrganisationDetailedController::class, 'getDtrs']);

    });
        

    Route::group(['prefix' => 'risk-insights'], function () {
        Route::get('/dashboard', [RiskInsightsDashboardController::class, 'index']);
        Route::get('/dashboard/company', [RiskInsightsDashboardController::class, 'company']);
        Route::get('/dashboard/location', [RiskInsightsDashboardController::class, 'location']);
        Route::get('/dashboard/risk', [RiskInsightsDashboardController::class, 'risk']);
        Route::get('/benchmarking', [RiskInsightsBenchmarkController::class, 'index']);
        Route::get('/benchmarking/organisation', [RiskInsightsBenchmarkController::class, 'organisation']);
        Route::get('/portfolio-distribution', [RiskInsightsPortfolioController::class, 'index']);

        Route::get('/store-rafa-result', [RiskInsightController::class, 'storeRAFAResult']);
        Route::post('/update-risk-report', [RiskInsightController::class, 'updateRiskReport']);
        Route::post('/finalize-risk-report', [RiskInsightController::class, 'finalizeRiskReport']);
        Route::post('/process-files-for-rafa', [RiskInsightController::class, 'processFilesForRAFA']);
        Route::get('/risk-report-data/{documentId}', [RiskInsightController::class, 'getRiskReportData']);
        Route::put('/lock-risk-report-to-user', [RiskInsightController::class, 'lockRiskReportToUser']);
        Route::post('/update-document-status', [RiskInsightController::class, 'updateDocumentStatus']);
        Route::post('/remove-document-nhitl', [RiskInsightController::class, 'removeDocumentNHITL']);
        Route::post('/submit-review-weightage', [RiskInsightController::class, 'submitReviewWeightage']);

        Route::get('/poll', [RiskInsightsPollController::class, 'poll']);

        Route::get('/documents-for-board', [RiskInsightBoardController::class, 'getDocumentsForBoard']);
        Route::post('/document-assignment', [RiskInsightBoardController::class, 'documentAssignment']);
    });

    Route::post('/pause-user-notification', [PauseUserNotificationController::class, 'pauseUserNotification']);
});

Route::prefix('/auth')->group(function() {
     Route::post('/social/{provider}', [SocialLoginController::class, 'login']);
});
