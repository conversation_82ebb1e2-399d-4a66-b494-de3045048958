<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('organisation_locations', function (Blueprint $table) {
            $table->string('segment_name')->nullable()->after('tiv');
            $table->string('rating_code')->nullable()->after('segment_name');
            $table->string('treaty_class_occupancy')->nullable()->after('rating_code');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('organisation_locations', function (Blueprint $table) {
            $table->dropColumn('segment_name');
            $table->dropColumn('rating_code');
            $table->dropColumn('treaty_class_occupancy');
        });
    }
};
