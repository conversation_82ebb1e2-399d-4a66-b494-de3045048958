<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('organisations', function (Blueprint $table) {
            $table->float('line_size', 10, 2)->nullable()->after('wellbeing_assessment');
            $table->float('risk_score', 10, 2)->nullable()->after('line_size');
            $table->string('premium')->nullable()->after('risk_score');
            $table->string('position')->nullable()->after('premium');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('organisations', function (Blueprint $table) {
            $table->dropColumn('line_size');
            $table->dropColumn('risk_score');
            $table->dropColumn('premium');
            $table->dropColumn('position');
        });
    }
};
