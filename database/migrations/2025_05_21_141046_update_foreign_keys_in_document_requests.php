<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('document_requests', function (Blueprint $table) {
            $table->dropForeign(['assigned_to_user']);
            $table->dropForeign(['uploaded_by']);
            $table->dropForeign(['updated_by']);

            $table->foreign('assigned_to_user')->references('id')->on('liberty_users');
            $table->foreign('uploaded_by')->references('id')->on('liberty_users');
            $table->foreign('updated_by')->references('id')->on('liberty_users');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('document_requests', function (Blueprint $table) {
            $table->dropForeign(['assigned_to_user']);
            $table->dropForeign(['uploaded_by']);
            $table->dropForeign(['updated_by']);

            $table->foreign('assigned_to_user')->references('id')->on('users');
            $table->foreign('uploaded_by')->references('id')->on('users');
            $table->foreign('updated_by')->references('id')->on('users');
        });
    }
}; 