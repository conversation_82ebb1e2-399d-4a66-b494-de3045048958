<?php

namespace App\Traits;

use App\Helpers\Helper;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Response;

trait ChecksExpiredPassword
{
    public function isPasswordExpired($user): bool
    {
        if (empty($user)) {
            return false;
        }

        $effectivityDate = Carbon::parse(config('app.password_expired_effectivity_date'));
        
        return (Helper::shouldTakeEffect($effectivityDate) && $user?->isPasswordExpired());
    }

    public function getPasswordExpiredResponse(): JsonResponse
    {
        return Response::json(
            [
                'response' => 'error',
                'message' => 'The User password has expired.',
                'password_expired' => true
            ]
        );
    }
}
