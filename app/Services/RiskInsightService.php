<?php

namespace App\Services;

use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\Organisation;
use App\Models\RiskInsights\RiskInsightAuditLogs;
use App\Models\RiskInsights\RiskInsightFinalAuditLogs;
use App\Models\RiskInsights\DocumentRequest;
use App\Services\RAFAService;

class RiskInsightService
{
    public function __construct()
    {

    }

    public function getRiskReportDataByDocumentId($documentId)
    {
        // ToDo: get by documentId
        $document = DocumentRequest::with('assignedUser')->where('id', (int)$documentId)->first();
        $auditLogs = RiskInsightAuditLogs::where('document_id', (int)$documentId)->orderBy('version', 'desc')->first();
        // $rafaService = new RAFAService();
        // // TODO: remove this and fetch the latest audit log
        // $auditLogs = $rafaService->getDetailedResult('1', '1');
        return (object)[
            'document' => $document,
            'auditLogs' => $auditLogs,
        ];
    }

    public function getFinalRiskReportDataByDocumentId($documentId)
    {
        $document = DocumentRequest::first();
        $auditLogs = RiskInsightFinalAuditLogs::where('document_id', $documentId)->first();
        return (object)[
            'document' => $document,
            'auditLogs' => $auditLogs,
        ];
    }

    public function saveRAFAResult($documentId, $updatedBy, $fileLocationData, $locationIds, $orgId, $isNHITL = false)
    {
        $rafaService = new RAFAService();
        $rafaData = $rafaService->getDetailedResult('1', '1');

        if (!is_array($rafaData) && !is_object($rafaData)) {
            throw new \Exception('RAFA data must be an array or object');
        }

        // Convert string parameters to integers
        $documentId = (int)$documentId;
        $updatedBy = (int)$updatedBy;

        RiskInsightAuditLogs::create([
            'document_id' => (int)$documentId,
            'metadata_update' => $rafaData,
            'selected_location_ids' => $locationIds,
            'mapped_locations' => $this->calculateLocationWeightage($orgId, $fileLocationData),
            'updated_by' => $updatedBy,
            'version' => 1,
        ]);

        DocumentRequest::where('id', $documentId)->update([
            'status' => $isNHITL ? DocumentRequest::STATUS_COMPLETED : DocumentRequest::STATUS_OPEN
        ]);

        return $rafaData;

    }

    /**
     * Calculate the location weightage based on location TIV and overall TIV
     *
     * @param int $orgId
     * @param array $fileLocationData
     * @return array
     */
    private function calculateLocationWeightage($orgId, $fileLocationData)
    {
        $mappedSelectedLocationWithFiles = [];
        $overallTiv = 0;
        foreach ($fileLocationData as $fileLocation) {
            $mappedSelectedLocationWithFiles[$fileLocation['locationId']] = [
                'organisation_id' => $orgId,
                'location'        => $fileLocation['locationName'],
                'tiv'             => $fileLocation['locationTiv'],
                'weightage'       => 0,
            ];

            $overallTiv += $fileLocation['locationTiv'];
        }

        foreach ($mappedSelectedLocationWithFiles as $key => $value) {
            $mappedSelectedLocationWithFiles[$key]['weightage'] = round($value['tiv'] / $overallTiv * 100, 2);
        }

        return $mappedSelectedLocationWithFiles;
    }

    public function updateRiskReport($documentId, $metadata, $updatedBy)
    {
        $version = (RiskInsightAuditLogs::where('document_id', (int)$documentId)->max('version') ?? 0) + 1;
        RiskInsightAuditLogs::create([
            'document_id' => (int)$documentId,
            'metadata_update' => $metadata,
            'updated_by' => $updatedBy,
            'version' => $version,
        ]);
    }

    public function finalizeRiskReport($documentId)
    {
        $document = DocumentRequest::where('id', (int)$documentId)->first();
        $latestAuditLog = RiskInsightAuditLogs::where('document_id', (int)$documentId)->orderBy('version', 'desc')->first();
        RiskInsightFinalAuditLogs::create([
            'document_id' => (int)$documentId,
            'metadata' => $latestAuditLog->metadata_update,
            'finalized_by' => $document->assigned_to_user,
        ]);

        $document = DocumentRequest::where('id', (int)$documentId)->update([
            'is_reviewed' => 1,
        ]);

        return $document;
    }

    public function createDocument($documentId, $metadata, $updatedBy)
    {
        RiskInsightDocument::create([
            'document_id' => $documentId,
            'metadata' => $metadata,
        ]);
        RiskInsightAuditLogs::create([
            'document_id' => $documentId,
            'metadata_update' => $metadata,
        ]);
    }

    public function getLatestAuditLog($documentId)
    {
        $auditLog = new RiskInsightAuditLogs();
        $latestVersion = $auditLog->getLatestVersion($documentId);
        return $latestVersion;
    }

    public function lockRiskReportToUser($documentId, $userId)
    {
        $document = DocumentRequest::where('id', (int)$documentId)->first();
        $document->assigned_to_user = $userId;
        $document->status = DocumentRequest::STATUS_UNDER_REVIEW;
        $document->save();
        return $document;
    }

    public function updateDocumentStatus($document, $user)
    {
        if ($document->do_skip_human_in_the_loop) {
            $document->status = DocumentRequest::STATUS_COMPLETED;
            $document->save();
            return $document;
        }

        $status = $document->status;
        $isUnderwriter = $user->role === 'underwriter';
        $isRiskEngineer = $user->role === 'risk-engineer';

        switch ($status) {
            case DocumentRequest::STATUS_OPEN:
                if($isUnderwriter
                    || ($isRiskEngineer && !$document->disable_risk_engineer_evaluation)
                ) {
                    $document->assigned_to_user = $user->id;
                    $document->status = DocumentRequest::STATUS_UNDER_REVIEW;
                    $document->save();
                }
                return $document;
            case DocumentRequest::STATUS_REVIEWED_BY_RE:
                if($isUnderwriter) {
                    $document->assigned_to_user = $user->id;
                    $document->status = DocumentRequest::STATUS_UNDER_REVIEW;
                    $document->save();
                }
                return $document;
            case DocumentRequest::STATUS_UNDER_REVIEW:
                $document->status = $isRiskEngineer ? DocumentRequest::STATUS_REVIEWED_BY_RE : DocumentRequest::STATUS_COMPLETED;
                $document->assigned_to_user = $isRiskEngineer ? null : $document->assigned_to_user;
                // finalize or add to database
                $document->save();

                if ($isRiskEngineer) {
                    // send email to UWs
                    $uwEmails = $this->extractUW($document->organisation_id);
                    $this->sendNotification($uwEmails);
                }
                return $document;
            default:
                return $document;
        }
    }

    private function extractUW($orgId): array
    {
        $underwriter = Organisation::where('id', $orgId)->first()->orgUnderwriter;

        $uwEmails = [];

        $uwIds = [];
        foreach ($underwriter as $uw) {
            $uwIds[] = $uw->user_id;
        }

        if (count($uwIds) > 0) {
            $underwriters = LibertyUser::whereIn('id', $uwIds)->get(['first_name', 'last_name', 'email']);
            foreach ($underwriters as $underwriter) {
                $fullName = trim($underwriter->first_name . ' ' . $underwriter->last_name);
                $uwEmails[$fullName] = $underwriter->email;
            }
        }

        return $uwEmails;
    }

    private function sendNotification(array $emails): void
    {
        foreach ($emails as $fullName => $email) {
            try {
                (new Mailqueue())->queue(
                    $email,
                    $fullName,
                    'Risk Insight Document Notification',
                    'emails.risk-insight.document-reviewed', 
                    [
                        'fullName' => $fullName,
                        'documentLink' => '',
                    ]
                );
            } catch (\Exception $e) {
                \Log::error('Error sending notification to ' . $fullName . ': ' . $e->getMessage());
            }
        }
    }

    public function removeDocumentNHITL($documentId)
    {
        $document = DocumentRequest::where('id', (int)$documentId)->first();
        $document->do_skip_human_in_the_loop = 0;
        $document->status = DocumentRequest::STATUS_OPEN;
        $document->save();
        return $document;
    }
}