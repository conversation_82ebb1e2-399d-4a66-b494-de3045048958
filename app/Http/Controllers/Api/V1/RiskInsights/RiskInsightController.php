<?php

namespace App\Http\Controllers\Api\V1\RiskInsights;

use App\Http\Controllers\Controller;
use App\Models\LibertyUser;
use App\Models\RiskInsights\DocumentRequest;
use App\Models\RiskInsights\RiskInsightAuditLogs;
use App\Services\RAFAService;
use App\Services\RiskInsightService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class RiskInsightController extends Controller
{

    public function __construct(
            private RiskInsightService $riskInsightService
    ) {}

    public function userHasAccessToDocument($document, $user)
    {
        // processing
        // no access

        // under review
        // no access if user not assigned

        // reviewed by re
        // uw has access

        // ready for review
        // uw has access, re has access if skip re review is false

        // completed
        // all access

        $isUnderwriter = $user->role === 'underwriter';
        $isRiskEngineer = $user->role === 'risk-engineer';

        switch ($document->status) {
            case DocumentRequest::STATUS_PROCESSING:
                return false;
            case DocumentRequest::STATUS_UNDER_REVIEW:
                return $document->assigned_to_user === $user->id;
            case DocumentRequest::STATUS_OPEN:
                if ($isUnderwriter) return true;
                if ($isRiskEngineer && !$document->disable_risk_engineer_evaluation) return true;
                return false;
            case DocumentRequest::STATUS_REVIEWED_BY_RE:
                return $isUnderwriter;
            case DocumentRequest::STATUS_COMPLETED:
                return true;
            default:
                return false;
        }
    }
    // Should be called by cron job if status is 'completed' from RAFA
    // First version in audit logs
    // Send mail to risk engineer/s
    public function storeRAFAResult(Request $request)
    {
        // TODO: request should have document_id, updated_by
        $riskInsightService = new RiskInsightService();
        $response = $riskInsightService->saveRAFAResult('1', 'test');
        // $riskInsightService->saveRAFAResult($request->document_id, $request->metadata, $request->updated_by);

        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function processFilesForRAFA(Request $request)
    {
        // Check if files are being received
        if ($request->has('files')) {
            \Log::info('Files received:', $request->input('files'));
        } else {
            \Log::warning('No files received in the request.');
            return response()->json([
                'response' => 'error',
                'message' => 'No files received in the request.',
            ]);
        }

        $rafaService = new RAFAService();

        $response = $rafaService->uploadFiles($request->input('files'), $request->input('lineOfBusiness'));

        $userId = LibertyUser::where('email', $request->input('uploadedBy'))->first()->id;

        $orgId = (int)$request->input('organizationId');

        $createDocRequestData = [
            'name' => 'RAFA',
            'document_data' => $response,
            'organisation_id' => $orgId,
            'uploaded_by' => $userId,
            'updated_by' => $userId,
            'disable_risk_engineer_evaluation' => $request->boolean('disableRiskEngineerEvaluation'),
            'do_skip_human_in_the_loop' => $request->boolean('skipHumanInTheLoop'),
            'status' => DocumentRequest::STATUS_PROCESSING,
        ];

        $documentRequest = DocumentRequest::create($createDocRequestData);

        \Log::info('Document Request:', ['id' => $documentRequest->id]);

        // TODO: adding the GET /result endpoint should be done by cron job
        // $reponseArr = json_decode($response, true);

        $locationIds = $request->selectedLocationIds ?? [];
        $fileLocationData = json_decode($request->fileLocationData, true) ?? [];

        // TODO: fix saving in logs
        (new RiskInsightService())->saveRAFAResult(
            $documentRequest->id,
            $userId,
            $fileLocationData,
            $locationIds,
            $orgId,
            $request->boolean('skipHumanInTheLoop')
        );


        return response()->json([
            'response' => 'success',
            'data' => 'Successfully created document request',
            'document_id' => $documentRequest->id
        ]);
    }

    public function updateRiskReport(Request $request)
    {
        \Log::info('updateRiskReport called', ['data' => $request->all()]);
        $riskInsightService = new RiskInsightService();
        $response = $riskInsightService->getRiskReportDataByDocumentId($request->documentId);
        $libertyUser = LibertyUser::where('id', $response->document->assigned_to_user)->first();

        if (
            !$libertyUser
            || !$this->userHasAccessToDocument($response->document, $libertyUser)
        ) {
            return response()->json([
                'status' => 401,
                'message' => 'unauthorised'
            ]);
        }
        
        $riskInsightService->updateRiskReport($request->documentId, json_decode($request->reportMetadata), $request->updatedBy);
    }

    public function finalizeRiskReport(Request $request)
    {
        $riskInsightService = new RiskInsightService();
        $response = $riskInsightService->getRiskReportDataByDocumentId($request->documentId);
        $libertyUser = LibertyUser::where('id', $response['document']->assigned_to_user)->first();

        if($libertyUser->role !== 'underwriter'){
            return response()->json([
                'status' => 401,
                'message' => 'unauthorised'
            ]);
        }

        $response = $riskInsightService->finalizeRiskReport($request->documentId);
        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function getRiskReportData($documentId, Request $request)
    {
        $riskInsightService = new RiskInsightService();
        $response = $riskInsightService->getRiskReportDataByDocumentId($documentId);
        $libertyUser = LibertyUser::where('id', $request->userId)->first();

        if (
            !$libertyUser
            || !$this->userHasAccessToDocument($response->document, $libertyUser)
        ) {
            return response()->json([
                'status' => 401,
                'message' => 'unauthorised'
            ]);
        }

        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function getFinalRiskReportData($documentId)
    {
        $riskInsightService = new RiskInsightService();
        $response = $riskInsightService->getFinalRiskReportDataByDocumentId($documentId);
        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function lockRiskReportToUser(Request $request)
    {
        $riskInsightService = new RiskInsightService();
        $document = $riskInsightService->getRiskReportDataByDocumentId($request->documentId);
        $libertyUser = LibertyUser::where('id', $request->userId)->first();

        if (
            !$libertyUser
            || !$this->userHasAccessToDocument($document->document, $libertyUser)
        ) {
            return response()->json([
                'status' => 401,
                'message' => 'unauthorised'
            ]);
        }

        $response = $riskInsightService->lockRiskReportToUser($request->documentId, $request->userId);
        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function updateDocumentStatus(Request $request)
    {
        $riskInsightService = new RiskInsightService();
        $document = $riskInsightService->getRiskReportDataByDocumentId($request->documentId);
        $userId = $request->has('userId') ? $request->input('userId') : $document->document->assigned_to_user;
        $libertyUser = LibertyUser::where('id', $userId)->first();

        if (
            !$libertyUser
            || !$this->userHasAccessToDocument($document->document, $libertyUser)
        ) {
            return response()->json([
                'status' => 401,
                'message' => 'unauthorised'
            ]);
        }

        $response = $riskInsightService->updateDocumentStatus($document->document, $libertyUser);

        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function removeDocumentNHITL(Request $request)
    {
        $riskInsightService = new RiskInsightService();
        $response = $riskInsightService->removeDocumentNHITL($request->documentId);
        return response()->json([
            'response' => 'success',
            'data' => $response,
        ]);
    }

    public function submitReviewWeightage(Request $request)
    {
        $latestAuditLog = $this->riskInsightService->getLatestAuditLog((int)$request->documentId);

        // Create a new array with all the mapped locations
        $mappedLocations = $latestAuditLog->mapped_locations;
        
        // Update the weightages in the new array
        $weightages = $request->weightages;
        foreach ($weightages as $locationId => $weightage) {
            if (isset($mappedLocations[$locationId])) {
                $mappedLocations[$locationId]['weightage'] = $weightage;
            }
        }

        // Set the entire mapped_locations array at once
        $latestAuditLog->mapped_locations = $mappedLocations;
        $latestAuditLog->save();
        
        return response()->json([
            'response' => 'success',
            'data' => $latestAuditLog,
        ]);
    }
}
