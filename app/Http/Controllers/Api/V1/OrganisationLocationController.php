<?php

namespace App\Http\Controllers\Api\V1;

use App\Jobs\Previsico\GenerateAssetsList;
use App\Http\Controllers\BaseController;
use Exception;
use App\Models\OrganisationLocations;
use App\Models\OrganisationLocationBulkUploadLogs;
use App\Models\Postcode\Postcode;
use App\Models\Previsico\PrevisicoAsset;
use App\Models\RGAttribute;
use App\Models\RiskGrading\RgLocationGrading;
use App\Models\RiskGrading\RgLocationGradingLog;
use App\Models\RiskGrading;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;

class OrganisationLocationController extends BaseController
{
    const GRADINGS = [
        'poor' => 0,
        'below_average' => 0.25,
        'average' => 0.5,
        'good' => 0.75,
        'superior' => 1,
    ];

    const BANDING_OUTPUTS = [
        'Poor' => [
            'min' => 0,
            'max' => 24,
        ],
        'Below Average' => [
            'min' => 25,
            'max' => 49,
        ],
        'Average' => [
            'min' => 50,
            'max' => 69,
        ],
        'Above Average' => [
            'min' => 70,
            'max' => 84,
        ],
        'Superior' => [
            'min' => 85,
            'max' => 100,
        ],
    ];

    public function __construct()
    {
    }

    public function store(Request $request)
    {
        $data = $request->except('_token');

        // Check if created via manual location creation
        if (
            isset($data['location_creation_type'])
            && !empty($data['location_creation_type'])
            && $data['location_creation_type'] === 'manual'
        ) {
            $organisation_id = $data['organisation_id'];

            $location_exist = OrganisationLocations::where('location_name', $data['location_name'])
                ->where('organisation_id', $data['organisation_id'])
                ->exists();

            $location_id_exist = OrganisationLocations::where('location_id', $data['location_id'])
                ->where('organisation_id', $data['organisation_id'])
                ->exists();

            if ($location_exist) {
                $error[] = "Location Name already exist, please choose another location name.";
            }

            if ($location_id_exist) {
                $error[] = "Location ID already exist, please choose another location id.";
            }

            if (isset($error) && !empty($error)) {
                return response()->json([
                    'status'         => 'error',
                    'error_messages' => $error,
                ]);
            }

            OrganisationLocations::create($data);

            // Enqueue a generate asset job
            // See: GenerateAssetsList::generateList
            GenerateAssetsList::queueJob();

            Cache::put('organisation_location_last_updated', now()->timestamp);

            return response()->json([
                'status'  => 'success',
                'message' => 'location has been create successfully.',
            ]);
        } else {
            // Autmatic and via upload
            foreach ($data as $location) {
                $orgLoc = OrganisationLocations::where('location_name', $location['location_name'])
                    ->where('organisation_id', $location['organisation_id'])
                    ->first();

                if ($orgLoc) {
                    OrganisationLocations::where('id', $orgLoc->id)->update($location);
                } else {
                    OrganisationLocations::create($location);
                }
            }

            // Enqueue a generate asset job
            // See: GenerateAssetsList::generateList
            GenerateAssetsList::queueJob();

            return response()->json([
                'status'  => 'success',
                'message' => count($data) . ' locations imported successfully.',
            ]);
        }
    }

    public function update(Request $request)
    {
        $input = $request->all();

        $validator = Validator::make($input, [
            'location_name' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'errors'   => $validator->messages()->toArray(),
            ], 200);
        } else {
            $data     = (object)$input;
            $location = OrganisationLocations::with('organisation')->find($data->id);

            if (isset($location->id)) {

                // check if there are associated grading values
                if ($request->has('updated_gradings')) {
                    $gradings   = $request->get('updated_gradings');
                    $policy_ids = $request->get('updated_policy_ids');

                    $createdAt = date('Y-m-d H:i:s');

                    foreach ($gradings as $attribute => $value) {
                        $fields        = explode("_", $attribute);
                        $attr          = $fields[0];
                        $policyTypeId  = $fields[1];
                        $attributeType = $fields[2];

                        $riskGrading = RgLocationGrading::firstOrCreate([
                            'organisation_location_id' => $location->id,
                            'policy_type_id'           => $policyTypeId,
                            'attribute_type'           => $attributeType,
                            'attribute'                => str_replace("-", " ", $attr),
                        ]);

                        $defaultValue = 'Not Applicable / Not Assessed';

                        $riskGrading->value = $value ? $value : $defaultValue;
                        $riskGrading->save();

                        if ($attributeType == 'attribute') {
                            RgLocationGradingLog::create([
                                'organisation_location_id' => $location->id,
                                'policy_type_id'           => $policyTypeId,
                                'attribute'                => str_replace("-", " ", $attr),
                                'value'                    => $value,
                                'survey_id'                => 0,
                                'created_at'               => $createdAt
                            ]);
                        }
                    }
                }

                // Check if we need to generate new grid coordinates
                // for Previsico Asset record
                $generateNewPostCode          = ($location->postcode != $data->postcode);
                $updateAssetName              = ($location->location_name != $data->location_name);
                $location->location_id        = $data->location_id;
                $location->location_name      = $data->location_name;
                $location->postcode           = $data->postcode;
                $location->address_line_1     = $data->address_line_1;
                $location->address_line_2     = $data->address_line_2 ?? '';
                $location->city               = $data->city ?? '';
                $location->county             = $data->county ?? '';
                $location->country            = $data->country ?? '';
                $location->buildings          = $data->buildings ?? 0;
                $location->me                 = $data->me ?? 0;
                $location->stock              = $data->stock ?? 0;
                $location->other              = $data->other ?? 0;
                $location->gross_revenue      = $data->gross_revenue ?? 0;
                $location->indefinity_period  = $data->indefinity_period ?? 0;
                $location->icow               = $data->icow ?? 0;
                $location->tiv                = $data->tiv ?? 0;
                $location->save();

                // Check if there's an existing Previsico Asset for the location
                $assetUpdated = false;
                $assetDeleted = false;
                $asset = PrevisicoAsset::where('organisation_location_id', $location->id)->first();

                // Check if existing asset needs updating
                if (!empty($asset)) {
                    if (!empty($updateAssetName)) {
                        $asset->name = !empty($location->location_name)
                            ? $location->location_name
                            : $location->postcode;
                        $assetUpdated = true;
                    }

                    if (!empty($generateNewPostCode)) {
                        try {
                            $coords = (new Postcode)->get($location->postcode);
                            $asset->easting = $coords->eastings;
                            $asset->northing = $coords->northings;
                            $asset->latitude = $coords->latitude;
                            $asset->longitude = $coords->longitude;
                            $assetUpdated = true;
                        } catch (Exception $e) {
                            // If we encounter an error while fetching the updated postcode
                            // Delete the asset so that it won't be included in the new asset list
                            // to be generated
                            $asset->delete();
                            $assetDeleted = true;
                        }
                    }

                    if ($assetUpdated && !$assetDeleted) {
                        $asset->save();
                    }
                }

                // Save asset and enqueue a generate asset list job
                // only if there is an update to the Previsico Asset record
                // or if the asset was deleted
                // or if the location does not have an asset record and it's organisation has previsico access
                // See GenerateAssetsList::generateList
                if (
                    $assetUpdated
                    || $assetDeleted
                    || (empty($asset) && !empty($location->organisation->has_previsico_access))
                ) {
                    GenerateAssetsList::queueJob();
                }

                Cache::put('organisation_location_last_updated', now()->timestamp);

                return response()->json([
                    'response' => 'success',
                    'message'  => 'The Location data has been updated successfully',
                ]);
            } else {
                return response()->json([
                    'response' => 'error',
                    'message'  => 'Invalid Location data',
                ]);
            }
        }
    }

    public function delete($id)
    {
        $data = OrganisationLocations::find($id);
        if ($data) {
            $data->destroy($id);

            // Enqueue a generate asset job
            // See: GenerateAssetsList::generateList
            GenerateAssetsList::queueJob();

            Cache::put('organisation_location_last_updated', now()->timestamp);

            return response()->json([
                'response' => 'success',
                'message'  => 'The Location ' . $data->location_name . ' has been deleted successfully.',
            ]);
        } else {
            return response()->json([
                'status'  => 'error',
                'message' => 'error deleting location data',
            ]);
        }
    }

    public function getLocation($organisation_id)
    {

        $data = OrganisationLocations::where('organisation_id', $organisation_id)->get();

        if ($data) {
            return response()->json([
                'status'  => 'success',
                'message' => 'Get all data.',
                'data'    => $data,
            ]);
        } else {
            return response()->json([
                'status'  => 'error',
                'message' => 'No Available Data.',
                'data'    => null,
            ]);
        }
    }

    public function getLocationData($id)
    {
        $data = OrganisationLocations::with(['standardLocationsGradings'])->find($id);

        if (!$data) {
            $data = OrganisationLocations::find($id);
        }

        $finalizeSRG = [];
        $policyIds   = [];
        $orgPolicies = !empty($data->organisation->policyNumbers())
            ? $data->organisation->policyNumbers()
            : [];
        foreach ($orgPolicies as $policy) {
            if ($policy->policy_type_id === 3) {
                if (in_array(1, $policyIds)) {
                    $policyIds[] = 2;
                } elseif (in_array(2, $policyIds)) {
                    $policyIds[] = 1;
                } else {
                    array_push($policyIds, 1, 2);
                }
            } elseif (!in_array($policy->policy_type_id, $policyIds)) {
                $policyIds[] = $policy->policy_type_id;
            }
        }

        if (empty($data?->standardLocationsGradings) || $data?->standardLocationsGradings?->count() === 0) {
            $finalizeSRG = $this->getDefaultSRG($policyIds, $id, "");
        } else {
            $srg = $data->standardLocationsGradings ?? [];
            $policyTypesWithValues = [];
            $policyTypesWithValueArr = [];
            foreach ($srg as $grading) {
                if (in_array($grading->policy_type_id, $policyIds)) {
                    $policyTypesWithValueArr[] = $grading->policy_type_id;
                    $finalizeSRG[] = $grading;
                }
            }
            $policyTypesWithValues = array_unique($policyTypesWithValueArr);
            $default               = $this->getDefaultSRG($policyIds, $id, $policyTypesWithValues);
            $finalizeSRG           = array_merge($finalizeSRG, $default);
        }

        $data['finalize_standard_risk_gradings'] = $finalizeSRG;
        if ($data) {
            return response()->json([
                'status' => 'success',
                'data'   => $data,
            ]);
        } else {
            return response()->json([
                'status' => 'error',
                'data'   => null,
            ]);
        }
    }

    private function getDefaultSRG($policyIds, $locationId, $policyTypesWithValues = null)
    {
        $srgs = RGAttribute::whereIn('rg_policy_type_id', $policyIds)
            ->with('subAttributes')
            ->get();

        $formattedSrgs = [];
        if ($srgs?->isEmpty()) {
            return $formattedSrgs;
        }

        foreach ($srgs as $srg) {
            if (empty($policyTypesWithValues) || !in_array($srg->rg_policy_type_id, $policyTypesWithValues)) {
                $formattedSrgs[] = [
                    'id' => $srg->id,
                    'organisation_location_id' => $locationId,
                    'policy_type_id' => $srg->rg_policy_type_id,
                    'attribute' => $srg->attribute,
                    'attribute_type' => 'attribute',
                    'value' => 'Not Applicable / Not Assessed',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s'),
                ];
                if (!empty($srg->subAttributes)) {
                    foreach ($srg->subAttributes as $subAttr) {
                        $formattedSrgs[] = [
                            'id' => $subAttr->id,
                            'organisation_location_id' => $locationId,
                            'policy_type_id' => $srg->rg_policy_type_id,
                            'attribute' => $subAttr->sub_attribute,
                            'attribute_type' => 'sub-attribute',
                            'value' => 'Not Applicable / Not Assessed',
                            'created_at' => date('Y-m-d H:i:s'),
                            'updated_at' => date('Y-m-d H:i:s'),
                        ];
                    }
                }
            }
        }
        return $formattedSrgs;
    }

    public function riskGradingLogs(Request $request)
    {
        $data = $request->all();
        $organisation_location_id = $data['organisation_location_id'];

        $risk_grading_logs = RiskGrading::with('riskGradingLogs')
            ->where('organisation_location_id', $organisation_location_id)
            ->get();

        return response()->json([
            'status'  => 'success',
            'data'    => $risk_grading_logs,
            'message' => '',
        ]);
    }

    public function riskGradingLogOverview(Request $request)
    {
        $data                   = $request->all();
        $organisationLocationId = $data['organisation_location_id'];
        $policyTypes            = [];

        $policies = RgLocationGradingLog::select('policy_type_id')
            ->where('organisation_location_id', $organisationLocationId)
            ->groupBy('policy_type_id')
            ->get();

        $logSet = RgLocationGradingLog::select('survey_id', 'created_at')
            ->where('organisation_location_id', $organisationLocationId)
            ->groupBy('created_at')
            ->get();

        foreach ($policies as $policy) {
            $policyTypes[] = $policy->policy_type_id;
        }

        $attributes = RGAttribute::with(['policyType', 'subAttributes'])
            ->whereIn('rg_policy_type_id', $policyTypes)
            ->get();

        $riskGradingLogs = RgLocationGradingLog::where('organisation_location_id', $organisationLocationId)
            ->get();
        $this->recalculateGradings($organisationLocationId, $attributes, $riskGradingLogs);

        return response()->json([
            'status'     => 'success',
            'data'       => $riskGradingLogs,
            'attributes' => $attributes,
            'log_set'    => $logSet,
            'message'    => '',
        ]);
    }

    private function recalculateGradings($orgLocationId, $attributes, &$riskGradingLogs)
    {
        $result = [];
        $locationGrading = RgLocationGrading::where('organisation_location_id', $orgLocationId)
            ->pluck('value', 'attribute')
            ->toArray();
        $listOfAttributes = $attributes->toArray();

        foreach ($listOfAttributes as $attribute) {
            $attrName = $attribute['attribute'];
            if (!isset($locationGrading[$attrName])) {
                continue;
            }

            $totalPoints = 0;
            $totalPercentage = 0;
            $totalSubPoints = 0;
            $gradingVal = $locationGrading[$attrName];
            if ($gradingVal === 'not_applicable_not_assessed' || $gradingVal === 'Not Applicable / Not Assessed') {
                $subAttributes = $attribute['sub_attributes'];
                foreach ($subAttributes as $subAttr) {
                    $subAttrName = $subAttr['sub_attribute'];
                    $subValue = $locationGrading[$subAttrName];
                    $maxPoint = $subAttr['max_point'];

                    if ($subValue !== 'not_applicable_not_assessed' && $subValue !== 'Not Applicable / Not Assessed') {
                        $totalPoints += $maxPoint;
                        $totalSubPoints += $maxPoint * $this->gradingValue($subValue);
                    }
                }

                if ($totalSubPoints > 0) {
                    $totalPercentage = round(($totalSubPoints / $totalPoints) * 100);
                    // $rgLocGrading->value = $this->getBandingOutput($totalPercentage);
                    $result[$attrName] = $this->getBandingOutput($totalPercentage);
                }
            }
        }
        
        if (!empty($result)) {
            foreach ($riskGradingLogs as $gradingLog) {
                if (isset($result[$gradingLog->attribute])) {
                    $gradingLog->value = $result[$gradingLog->attribute];
                }
            }
        }
    }

    private function gradingValue($gradingText)
    {
        $grading = 0;
        switch ($gradingText) {
        case 'Superior':
            $grading = self::GRADINGS['superior'];
            break;
        case 'Average':
            $grading = self::GRADINGS['average'];
            break;
        case 'Above Average':
            $grading = self::GRADINGS['good'];
            break;
        case 'Below Average':
            $grading = self::GRADINGS['below_average'];
            break;
        }

        return $grading;
    }

    private function getBandingOutput($total)
    {
        foreach (self::BANDING_OUTPUTS as $key => $arrayValues) {
            if ($arrayValues['min'] <= $total && $arrayValues['max'] >= $total) {
                return $key;
            }
        }
    }

    public function bulkMatchLocations($organisation_id, Request $request)
    {
        $data = $request->all();
        $processedLocations = [];
        foreach ($data as $location) {
            $locationData = OrganisationLocations::where('location_name', $location['location_name'])
                ->where('organisation_id', $organisation_id)
                ->first();

            if ($locationData) {
                $location['id'] = $locationData->id;
                $location['action'] = 'update';
                $processedLocations[] = $location;
            } else {
                $location['action'] = 'create';
                $processedLocations[] = $location;
            }
        }

        $allLocations = OrganisationLocations::where('organisation_id', $organisation_id)->get();
        return [
            'status' => 'success',
            'processed_locations' => $processedLocations,
            'all_locations' => $allLocations,
        ];
    }

    public function bulkUploadLocations($organisation_id, Request $request)
    {
        $locationData = $request->all();
        $locationData = $locationData['processed_locations'];
        $processedLocations = [];
        $allowedFields = [
            'organisation_id',
            'location_name',
            'postcode',
            'address_line_1',
            'city',
            'country',
            'segment_name',
            'rating_code',
            'treaty_class_occupancy',
            'tiv',
        ];
        foreach ($locationData as $location) {
            $processedData = [];
            foreach ($allowedFields as $field) {
                if (isset($location[$field])) {
                    $processedData[$field] = $location[$field];
                }
            }
            if($location['action'] == 'create') {
                $response = OrganisationLocations::create($processedData);
                $processedData['id'] = $response->id;
                $processedData['action'] = 'create';
            } else if ($location['action'] == 'match') {
                $response = OrganisationLocations::where('id', (int)$location['matched_location_id'])->update($processedData);
                $processedData['id'] = $location['matched_location_id'];
                $processedData['action'] = 'match';
            } else if ($location['action'] == 'update') {
                $response = OrganisationLocations::where('id', (int)$location['id'])->update($processedData);
                $processedData['id'] = $location['id'];
                $processedData['action'] = 'update';
            }
            $processedLocations[] = $processedData;
        }

        OrganisationLocationBulkUploadLogs::create([
            'organisation_id' => $organisation_id,
            'processed_locations' => $processedLocations,
            'created_at' => now(),
        ]);
        return $processedLocations;
    }
}
