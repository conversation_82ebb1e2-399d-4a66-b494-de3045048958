<?php

namespace App\Models;

use App\Models\RiskGrading;
use App\Models\Organisation;
use App\Observers\HasDefaultFieldValue;
use Illuminate\Database\Eloquent\Model;
use App\Models\RiskGrading\RgLocationGrading;

class OrganisationLocations extends Model
{
    use HasDefaultFieldValue;

    protected $table = 'organisation_locations';
    protected $fillable = [
        'organisation_id',
        'location_id',
        'location_name',
        'postcode',
        'address_line_1',
        'address_line_2',
        'city',
        'county',
        'country',
        'buildings',
        'me',
        'stock',
        'other',
        'gross_revenue',
        'indefinity_period',
        'icow',
        'tiv',
    ];

    protected $hidden = [];

    /**
     * Default values for these fields will be set on 'saving' event if not set
     *
     * @return array
     */
    public function setDefaultValuesForFields(): array
    {
        return [
            'city'       => "",
            'location_name'       => "",
        ];
    }

    public function organisation()
    {
        return $this->belongsTo(Organisation::class);
    }

    public function gradings()
    {
        return $this->hasMany(RiskGrading::class, 'organisation_location_id', 'id')
            ->where('value', '!=', 'No Data')
            ->orderBy('attribute', 'ASC');
    }

    public function locationsGradings()
    {
        return $this->hasMany(RiskGrading::class, 'organisation_location_id', 'id')
            ->orderBy('attribute', 'ASC');
    }

    public function standardLocationsGradings()
    {
        return $this->hasMany(RgLocationGrading::class, 'organisation_location_id', 'id');
    }

    public function standardLocationsGradingsOverview()
    {
        return $this->hasMany(RgLocationGrading::class, 'organisation_location_id', 'id')
            ->where('attribute_type', 'attribute');
    }

    public function standardLocationsSubGradingsOverview()
    {
        return $this->hasMany(RgLocationGrading::class, 'organisation_location_id', 'id')
            ->where('attribute_type', 'sub-attribute');
    }
}
