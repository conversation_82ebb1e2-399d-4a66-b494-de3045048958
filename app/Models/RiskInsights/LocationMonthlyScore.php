<?php

namespace App\Models\RiskInsights;

use App\Models\OrganisationLocations;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LocationMonthlyScore extends Model
{
    use HasFactory;

    protected $table = 'rg_locations_monthly_score';

    protected $fillable = [
        'organisation_location_id',
        'coverage',  // Y-m-d format
        'score',
    ];
    
    public function location()
    {
        return $this->belongsTo(OrganisationLocations::class);
    }

    public function getMonth()
    {
        return date('F', strtotime($this->coverage));
    }

    public function getYear()
    {
        return date('Y', strtotime($this->coverage));
    }
}
