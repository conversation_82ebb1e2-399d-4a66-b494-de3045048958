<?php

namespace App\Models;

use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\SoftDeletes as SoftDeletingTrait;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;

class OrganisationLocationBulkUploadLogs extends Model
{
    use SoftDeletingTrait;

    /**
     * @var string
     */
    protected $connection = 'mongodb';

    /**
     * @var array
     */
    protected $collection = 'organisation_location_bulk_upload_logs';

    /**
     * @var array
     */
    protected $guarded = [];
}
