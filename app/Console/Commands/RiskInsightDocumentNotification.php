<?php

namespace App\Console\Commands;

use App\Models\LibertyUser;
use App\Models\Mailqueue;
use App\Models\RiskInsights\DocumentRequest;
use App\Services\RAFAService;
use App\Services\RiskInsightService;
use Illuminate\Console\Command;

class RiskInsightDocumentNotification extends Command
{
    /**
     * The status of the document is completed
     *
     * @var string STATUS_COMPLETED
     */
    const STATUS_COMPLETED = 'completed';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'risk-insight:check-document-changes';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the status of the risk insight document that was submitted and notify risk engineers and underwriters';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Fetching processing document requests...');

        $documentRequests = DocumentRequest::with([
            'organisation:id', 
            'organisation.orgRiskEngineer:id,organisation_id,user_id,email', 
            'organisation.orgUnderwriter:id,organisation_id,user_id,email'
        ])->where('status', 'processing')->get();

        $this->info('Found ' . $documentRequests->count() . ' processing document requests');

        foreach ($documentRequests as $documentRequest) {
            $this->checkDocumentStatus($documentRequest);
        }
    }

    private function checkDocumentStatus($documentRequest)
    {
        $requestId = $documentRequest->document_data['requestId'];

        $this->info('Checking document status for ' . $requestId);

        $response = (new RAFAService())->checkRequestFile($requestId);
        $result = json_decode($response, true);

        if ($result['status'] === self::STATUS_COMPLETED) {
            $this->info('Document status for ' . $requestId . ' is completed! Notifying risk engineers and underwriters...');

            $isRiskEngineerReviewDisabled = $documentRequest->disable_risk_engineer_review;
            $isNHITL = $documentRequest->do_skip_human_in_the_loop;
            $emails = $this->extractUWAndRE($documentRequest, $isRiskEngineerReviewDisabled);
            $this->sendNotification($emails, $isNHITL);
            
            if ($isNHITL) {
                $this->info('Document is NHITL. Notifying risk engineers and underwriters...');
                $riskInsightService = new RiskInsightService();
                $riskInsightService->updateDocumentStatus($documentRequest, null);
            } else {
                $documentRequest->status = DocumentRequest::STATUS_OPEN;
                $documentRequest->save();
            }
        } else {
            $this->info('Document status for ' . $requestId . ' is still in process.');
        }
    }

    private function extractUWAndRE(object $documentRequest, bool $isRiskEngineerReviewDisabled): array
    {
        $underwriter = $documentRequest->organisation->orgUnderwriter;
        $riskEngineer = $documentRequest->organisation->orgRiskEngineer;

        $uwEmails = [];
        $reEmails = [];

        $uwIds = [];
        foreach ($underwriter as $uw) {
            $uwIds[] = $uw->user_id;
        }

        $reIds = [];
        if (!$isRiskEngineerReviewDisabled) {
            foreach ($riskEngineer as $re) {
                $reIds[] = $re->user_id;
            }
        }

        if (count($uwIds) > 0) {
            $underwriters = LibertyUser::whereIn('id', $uwIds)->get(['first_name', 'last_name', 'email']);
            foreach ($underwriters as $underwriter) {
                $fullName = trim($underwriter->first_name . ' ' . $underwriter->last_name);
                $uwEmails[$fullName] = $underwriter->email;
            }
        }

        if (count($reIds) > 0) {
            $riskEngineers = LibertyUser::whereIn('id', $reIds)->get(['first_name', 'last_name', 'email']);
            foreach ($riskEngineers as $riskEngineer) {
                $fullName = trim($riskEngineer->first_name . ' ' . $riskEngineer->last_name);
                $reEmails[$fullName] = $riskEngineer->email;
            }
        }

        return array_merge($uwEmails, $reEmails);
    }

    private function sendNotification(array $emails, bool $isNHITL = false): void
    {
        foreach ($emails as $fullName => $email) {
            try {
                (new Mailqueue())->queue(
                    $email,
                    $fullName,
                    'Risk Insight Document Notification',
                    $isNHITL ? 'emails.risk-insight.document-added-to-db' : 'emails.risk-insight.document-reviewed', 
                    [
                        'fullName' => $fullName,
                        'documentLink' => '',
                    ]
                );
            } catch (\Exception $e) {
                $this->error('Error sending notification to ' . $fullName . ': ' . $e->getMessage());
            }
        }
    }
}
